<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>APP环境兼容性测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #0056CC;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f0f0f0;
            border-left: 4px solid #007AFF;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #28a745;
        }
        .error {
            background: #ffe6e6;
            border-left-color: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffc107;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 APP环境兼容性测试</h1>
        
        <div class="info-box">
            <h3>🎯 测试目标</h3>
            <p>验证在uni-app APP-PLUS环境中，语音识别功能是否能够正常工作，特别是：</p>
            <ul>
                <li>✅ 修复Blob对象不可用的问题</li>
                <li>✅ 确保base64数据能正确保存为临时文件</li>
                <li>✅ 验证uni.uploadFile能正确调用硅基流动API</li>
                <li>✅ 确保返回真实的API响应数据</li>
            </ul>
        </div>
        
        <div class="warning-box">
            <h3>⚠️ 环境要求</h3>
            <p><strong>注意：</strong>此测试需要在uni-app的APP-PLUS环境中运行才能完全验证兼容性修复效果。</p>
            <p>在浏览器环境中，某些API可能表现不同。</p>
        </div>
        
        <div>
            <button id="checkEnvBtn" class="button">检查环境兼容性</button>
            <button id="testBase64Btn" class="button">测试Base64处理</button>
            <button id="testFileSystemBtn" class="button">测试文件系统</button>
            <button id="testUploadBtn" class="button">测试API上传</button>
            <button id="clearBtn" class="button">清除日志</button>
        </div>
        
        <div id="status" class="status">准备测试</div>
        
        <h3>📋 测试日志</h3>
        <div id="log" class="log">点击测试按钮开始验证APP环境兼容性...</div>
        
        <div class="info-box">
            <h3>🔧 修复内容</h3>
            <ul>
                <li><strong>移除Blob依赖</strong>：不再使用Blob对象，直接处理base64数据</li>
                <li><strong>优化文件处理</strong>：使用uni.getFileSystemManager()保存临时文件</li>
                <li><strong>改进上传逻辑</strong>：使用uni.uploadFile()直接上传到硅基流动API</li>
                <li><strong>增强错误处理</strong>：提供详细的错误信息和故障排除建议</li>
            </ul>
        </div>
    </div>

    <script>
        class AppCompatibilityTester {
            constructor() {
                this.checkEnvBtn = document.getElementById('checkEnvBtn');
                this.testBase64Btn = document.getElementById('testBase64Btn');
                this.testFileSystemBtn = document.getElementById('testFileSystemBtn');
                this.testUploadBtn = document.getElementById('testUploadBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.statusDiv = document.getElementById('status');
                this.logDiv = document.getElementById('log');
                
                this.bindEvents();
            }
            
            bindEvents() {
                this.checkEnvBtn.addEventListener('click', () => this.checkEnvironment());
                this.testBase64Btn.addEventListener('click', () => this.testBase64Processing());
                this.testFileSystemBtn.addEventListener('click', () => this.testFileSystem());
                this.testUploadBtn.addEventListener('click', () => this.testApiUpload());
                this.clearBtn.addEventListener('click', () => this.clearLog());
            }
            
            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.logDiv.textContent += `[${timestamp}] ${message}\n`;
                this.logDiv.scrollTop = this.logDiv.scrollHeight;
                console.log(message);
            }
            
            clearLog() {
                this.logDiv.textContent = '';
            }
            
            updateStatus(message, type = 'info') {
                this.statusDiv.textContent = message;
                this.statusDiv.className = `status ${type}`;
            }
            
            checkEnvironment() {
                this.log('🔍 开始检查环境兼容性...');
                this.updateStatus('正在检查环境...', 'info');
                
                const checks = [
                    { name: 'uni对象', check: () => typeof uni !== 'undefined' },
                    { name: 'plus对象', check: () => typeof plus !== 'undefined' },
                    { name: 'Blob对象', check: () => typeof Blob !== 'undefined' },
                    { name: 'FormData对象', check: () => typeof FormData !== 'undefined' },
                    { name: 'atob函数', check: () => typeof atob !== 'undefined' },
                    { name: 'fetch函数', check: () => typeof fetch !== 'undefined' }
                ];
                
                let passCount = 0;
                checks.forEach(({ name, check }) => {
                    const result = check();
                    if (result) {
                        this.log(`✅ ${name}: 可用`);
                        passCount++;
                    } else {
                        this.log(`❌ ${name}: 不可用`);
                    }
                });
                
                this.log(`\n📊 兼容性检查结果: ${passCount}/${checks.length} 项通过`);
                
                if (typeof uni !== 'undefined') {
                    this.log('✅ uni-app环境检测通过');
                    if (typeof plus !== 'undefined') {
                        this.log('✅ APP-PLUS环境检测通过');
                        this.updateStatus('APP环境兼容性良好', 'success');
                    } else {
                        this.log('⚠️ 非APP-PLUS环境，某些功能可能不可用');
                        this.updateStatus('非APP环境，功能受限', 'error');
                    }
                } else {
                    this.log('❌ 非uni-app环境');
                    this.updateStatus('环境不兼容', 'error');
                }
                
                // 检查关键修复点
                if (typeof Blob === 'undefined') {
                    this.log('🎯 确认Blob对象不可用 - 这正是我们要修复的问题');
                    this.log('💡 修复方案: 使用uni.uploadFile直接上传文件，避免使用Blob');
                }
            }
            
            testBase64Processing() {
                this.log('🔄 开始测试Base64数据处理...');
                this.updateStatus('正在测试Base64处理...', 'info');
                
                try {
                    // 创建测试用的base64数据
                    const testData = 'SGVsbG8gV29ybGQ='; // "Hello World" in base64
                    this.log(`📦 测试数据: ${testData}`);
                    
                    // 测试atob函数
                    if (typeof atob !== 'undefined') {
                        const decoded = atob(testData);
                        this.log(`✅ atob解码成功: ${decoded}`);
                    } else {
                        this.log('❌ atob函数不可用');
                        throw new Error('atob函数不可用');
                    }
                    
                    // 测试base64数据清理
                    const dataUrl = 'data:audio/mp3;base64,' + testData;
                    const cleaned = dataUrl.replace(/^data:audio\/[^;]+;base64,/, '');
                    this.log(`✅ Base64前缀清理成功: ${cleaned}`);
                    
                    // 测试字节数组转换
                    const binaryString = atob(cleaned);
                    const bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }
                    this.log(`✅ 字节数组转换成功，长度: ${bytes.length}`);
                    
                    this.updateStatus('Base64处理测试通过', 'success');
                    this.log('🎉 Base64数据处理功能正常');
                    
                } catch (error) {
                    this.log('❌ Base64处理测试失败');
                    this.log(`错误信息: ${error.message}`);
                    this.updateStatus('Base64处理测试失败', 'error');
                }
            }
            
            testFileSystem() {
                this.log('📁 开始测试文件系统功能...');
                this.updateStatus('正在测试文件系统...', 'info');
                
                if (typeof uni === 'undefined') {
                    this.log('❌ uni对象不可用，无法测试文件系统');
                    this.updateStatus('文件系统测试失败', 'error');
                    return;
                }
                
                try {
                    // 获取文件系统管理器
                    const fs = uni.getFileSystemManager();
                    this.log('✅ 文件系统管理器获取成功');
                    
                    // 测试临时文件路径生成
                    const tempFileName = `test_${Date.now()}.txt`;
                    let tempDirPath = '';
                    
                    // 根据环境确定临时目录
                    if (typeof plus !== 'undefined') {
                        tempDirPath = plus.io.convertLocalFileSystemURL('_doc/');
                        this.log(`✅ APP-PLUS环境，临时目录: ${tempDirPath}`);
                    } else {
                        this.log('⚠️ 非APP-PLUS环境，使用默认路径');
                    }
                    
                    const tempFilePath = tempDirPath + tempFileName;
                    this.log(`📝 临时文件路径: ${tempFilePath}`);
                    
                    // 测试文件写入
                    const testContent = 'VGVzdCBjb250ZW50'; // "Test content" in base64
                    
                    fs.writeFile({
                        filePath: tempFilePath,
                        data: testContent,
                        encoding: 'base64',
                        success: () => {
                            this.log('✅ 临时文件写入成功');
                            this.updateStatus('文件系统测试通过', 'success');
                            
                            // 清理测试文件
                            fs.unlink({
                                filePath: tempFilePath,
                                success: () => {
                                    this.log('🗑️ 测试文件清理完成');
                                },
                                fail: (error) => {
                                    this.log(`⚠️ 测试文件清理失败: ${error.errMsg}`);
                                }
                            });
                        },
                        fail: (error) => {
                            this.log('❌ 临时文件写入失败');
                            this.log(`错误信息: ${error.errMsg}`);
                            this.updateStatus('文件系统测试失败', 'error');
                        }
                    });
                    
                } catch (error) {
                    this.log('❌ 文件系统测试异常');
                    this.log(`错误信息: ${error.message}`);
                    this.updateStatus('文件系统测试失败', 'error');
                }
            }
            
            testApiUpload() {
                this.log('🚀 开始测试API上传功能...');
                this.updateStatus('正在测试API上传...', 'info');
                
                if (typeof uni === 'undefined') {
                    this.log('❌ uni对象不可用，无法测试API上传');
                    this.updateStatus('API上传测试失败', 'error');
                    return;
                }
                
                this.log('💡 注意: 这是一个模拟测试，不会实际调用API');
                this.log('🎯 验证uni.uploadFile方法是否可用...');
                
                if (typeof uni.uploadFile === 'function') {
                    this.log('✅ uni.uploadFile方法可用');
                    
                    // 模拟API调用参数
                    const mockParams = {
                        url: 'https://api.siliconflow.cn/v1/audio/transcriptions',
                        filePath: '/mock/path/audio.mp3',
                        name: 'file',
                        formData: {
                            'model': 'FunAudioLLM/SenseVoiceSmall',
                            'language': 'zh'
                        },
                        header: {
                            'Authorization': 'Bearer sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl'
                        }
                    };
                    
                    this.log('📋 API调用参数验证:');
                    this.log(`   URL: ${mockParams.url}`);
                    this.log(`   文件字段名: ${mockParams.name}`);
                    this.log(`   模型: ${mockParams.formData.model}`);
                    this.log(`   语言: ${mockParams.formData.language}`);
                    this.log(`   认证头: ${mockParams.header.Authorization.substring(0, 20)}...`);
                    
                    this.updateStatus('API上传功能验证通过', 'success');
                    this.log('🎉 API上传功能准备就绪');
                    this.log('💡 实际使用时将调用真实的硅基流动API');
                    
                } else {
                    this.log('❌ uni.uploadFile方法不可用');
                    this.updateStatus('API上传测试失败', 'error');
                }
            }
        }
        
        // 初始化测试器
        document.addEventListener('DOMContentLoaded', () => {
            new AppCompatibilityTester();
        });
    </script>
</body>
</html>
