<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>硅基流动API真实调用测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #0056CC;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f0f0f0;
            border-left: 4px solid #007AFF;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #28a745;
        }
        .error {
            background: #ffe6e6;
            border-left-color: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 硅基流动API真实调用测试</h1>
        
        <div class="info-box">
            <h3>📋 测试说明</h3>
            <p>此工具用于测试硅基流动API的真实调用，确保不返回任何模拟数据。</p>
            <p><strong>API密钥：</strong>sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl</p>
            <p><strong>API端点：</strong>https://api.siliconflow.cn/v1/audio/transcriptions</p>
        </div>
        
        <div>
            <button id="testApiBtn" class="button">测试API连接</button>
            <button id="testUploadBtn" class="button">测试文件上传</button>
            <button id="clearBtn" class="button">清除日志</button>
        </div>
        
        <div id="status" class="status">准备测试</div>
        
        <h3>📋 测试日志</h3>
        <div id="log" class="log">点击测试按钮开始验证硅基流动API真实调用...</div>
        
        <div class="info-box">
            <h3>✅ 验证要点</h3>
            <ul>
                <li><strong>真实API调用</strong>：确保调用真实的硅基流动API，不返回模拟数据</li>
                <li><strong>正确的请求格式</strong>：使用multipart/form-data格式上传音频文件</li>
                <li><strong>有效的认证</strong>：使用正确的API密钥进行认证</li>
                <li><strong>错误处理</strong>：正确处理网络错误和API错误</li>
            </ul>
        </div>
    </div>

    <script>
        class RealApiTester {
            constructor() {
                this.testApiBtn = document.getElementById('testApiBtn');
                this.testUploadBtn = document.getElementById('testUploadBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.statusDiv = document.getElementById('status');
                this.logDiv = document.getElementById('log');
                
                this.apiKey = 'sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl';
                this.apiUrl = 'https://api.siliconflow.cn/v1/audio/transcriptions';
                
                this.bindEvents();
            }
            
            bindEvents() {
                this.testApiBtn.addEventListener('click', () => this.testApiConnection());
                this.testUploadBtn.addEventListener('click', () => this.testFileUpload());
                this.clearBtn.addEventListener('click', () => this.clearLog());
            }
            
            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.logDiv.textContent += `[${timestamp}] ${message}\n`;
                this.logDiv.scrollTop = this.logDiv.scrollHeight;
                console.log(message);
            }
            
            clearLog() {
                this.logDiv.textContent = '';
            }
            
            updateStatus(message, type = 'info') {
                this.statusDiv.textContent = message;
                this.statusDiv.className = `status ${type}`;
            }
            
            async testApiConnection() {
                this.log('🔄 开始测试硅基流动API连接...');
                this.updateStatus('正在测试API连接...', 'info');
                
                try {
                    // 测试API端点是否可访问
                    const response = await fetch(this.apiUrl, {
                        method: 'OPTIONS',
                        headers: {
                            'Authorization': `Bearer ${this.apiKey}`
                        }
                    });
                    
                    this.log(`✅ API端点可访问，状态码: ${response.status}`);
                    this.log(`响应头: ${JSON.stringify([...response.headers.entries()])}`);
                    
                    if (response.status === 200 || response.status === 405) {
                        this.updateStatus('API连接正常', 'success');
                        this.log('🎉 硅基流动API连接测试通过');
                    } else {
                        this.updateStatus('API连接异常', 'error');
                        this.log(`⚠️ API返回异常状态码: ${response.status}`);
                    }
                    
                } catch (error) {
                    this.log('❌ API连接测试失败');
                    this.log(`错误信息: ${error.message}`);
                    this.updateStatus('API连接失败', 'error');
                    
                    // 检查是否是CORS问题
                    if (error.message.includes('CORS')) {
                        this.log('💡 提示: 这可能是CORS跨域问题，在实际应用中需要通过服务端代理');
                    }
                }
            }
            
            async testFileUpload() {
                this.log('🎤 开始测试文件上传到硅基流动API...');
                this.updateStatus('正在测试文件上传...', 'info');
                
                try {
                    // 创建一个测试用的音频文件（简单的WAV格式）
                    const testAudioBlob = this.createTestAudioBlob();
                    this.log(`📦 创建测试音频文件，大小: ${testAudioBlob.size} bytes`);
                    
                    // 构建FormData
                    const formData = new FormData();
                    formData.append('model', 'FunAudioLLM/SenseVoiceSmall');
                    formData.append('language', 'zh');
                    formData.append('file', testAudioBlob, 'test_audio.wav');
                    
                    this.log('📤 准备发送请求到硅基流动API...');
                    this.log(`API地址: ${this.apiUrl}`);
                    this.log(`认证密钥: ${this.apiKey.substring(0, 10)}...`);
                    
                    // 发送请求
                    const response = await fetch(this.apiUrl, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${this.apiKey}`
                        },
                        body: formData
                    });
                    
                    this.log(`📥 收到API响应，状态码: ${response.status}`);
                    
                    if (response.ok) {
                        const result = await response.json();
                        this.log('✅ API调用成功！');
                        this.log(`响应数据: ${JSON.stringify(result, null, 2)}`);
                        
                        if (result.text) {
                            this.log(`🎯 识别结果: ${result.text}`);
                            this.updateStatus('文件上传测试成功', 'success');
                            
                            // 验证这不是模拟数据
                            if (result.text.includes('模拟') || result.text.includes('测试') || result.text.includes('配置')) {
                                this.log('⚠️ 警告: 返回的可能是模拟数据，请检查实现');
                            } else {
                                this.log('🎉 确认返回的是真实的API响应数据');
                            }
                        } else {
                            this.log('⚠️ API响应缺少text字段');
                        }
                    } else {
                        const errorText = await response.text();
                        this.log('❌ API请求失败');
                        this.log(`错误状态码: ${response.status}`);
                        this.log(`错误信息: ${errorText}`);
                        this.updateStatus('文件上传测试失败', 'error');
                        
                        // 分析常见错误
                        if (response.status === 401) {
                            this.log('💡 提示: 认证失败，请检查API密钥是否正确');
                        } else if (response.status === 400) {
                            this.log('💡 提示: 请求格式错误，请检查音频文件格式');
                        } else if (response.status === 429) {
                            this.log('💡 提示: 请求频率过高，请稍后重试');
                        }
                    }
                    
                } catch (error) {
                    this.log('❌ 文件上传测试失败');
                    this.log(`错误信息: ${error.message}`);
                    this.log(`错误堆栈: ${error.stack}`);
                    this.updateStatus('文件上传测试失败', 'error');
                    
                    // 提供故障排除建议
                    if (error.message.includes('Failed to fetch')) {
                        this.log('💡 提示: 网络连接问题或CORS限制');
                        this.log('   - 检查网络连接');
                        this.log('   - 在生产环境中通过服务端代理调用API');
                    }
                }
            }
            
            createTestAudioBlob() {
                // 创建一个简单的WAV文件头
                const sampleRate = 16000;
                const numChannels = 1;
                const bitsPerSample = 16;
                const duration = 1; // 1秒
                const numSamples = sampleRate * duration;
                
                // WAV文件头 (44字节)
                const buffer = new ArrayBuffer(44 + numSamples * 2);
                const view = new DataView(buffer);
                
                // RIFF header
                const writeString = (offset, string) => {
                    for (let i = 0; i < string.length; i++) {
                        view.setUint8(offset + i, string.charCodeAt(i));
                    }
                };
                
                writeString(0, 'RIFF');
                view.setUint32(4, 36 + numSamples * 2, true);
                writeString(8, 'WAVE');
                writeString(12, 'fmt ');
                view.setUint32(16, 16, true);
                view.setUint16(20, 1, true);
                view.setUint16(22, numChannels, true);
                view.setUint32(24, sampleRate, true);
                view.setUint32(28, sampleRate * numChannels * bitsPerSample / 8, true);
                view.setUint16(32, numChannels * bitsPerSample / 8, true);
                view.setUint16(34, bitsPerSample, true);
                writeString(36, 'data');
                view.setUint32(40, numSamples * 2, true);
                
                // 生成简单的正弦波音频数据
                for (let i = 0; i < numSamples; i++) {
                    const sample = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.3;
                    view.setInt16(44 + i * 2, sample * 32767, true);
                }
                
                return new Blob([buffer], { type: 'audio/wav' });
            }
        }
        
        // 初始化测试器
        document.addEventListener('DOMContentLoaded', () => {
            new RealApiTester();
        });
    </script>
</body>
</html>
