<template>
	<view class="page">
		<!-- 头部 -->
		<view class="header">
			<view class="header-content">
				<text class="back-btn" @click="goBack">←</text>
				<text class="header-title">语音记账</text>
			</view>
		</view>
		
		<!-- 语音识别区域 -->
		<view class="voice-section">
			<view class="voice-container">
				<view class="voice-circle" :class="{recording: isRecording, processing: isProcessing}">
					<text class="voice-icon">🎤</text>
				</view>
				
				<text class="voice-status">{{voiceStatus}}</text>
				
				<view class="voice-controls">
					<button
						v-if="!isRecording && !isProcessing"
						class="record-btn"
						@click="startRecording"
					>
						开始录音
					</button>
					<button
						v-if="isRecording"
						class="stop-btn"
						@click="stopRecording"
					>
						停止录音
					</button>
					<button
						v-if="isRecording"
						class="cancel-btn"
						@click="cancelRecording"
					>
						取消
					</button>
				</view>
			</view>
		</view>
		
		<!-- 识别结果 -->
		<view v-if="recognitionResult" class="result-section">
			<view class="result-card">
				<view class="result-header">
					<text class="result-title">识别结果</text>
					<text class="result-edit" @click="editResult">编辑</text>
				</view>
				
				<view class="result-content">
					<text class="result-text">{{recognitionResult}}</text>
				</view>
				
				<!-- 解析后的记账信息 -->
				<view v-if="parsedRecord" class="parsed-info">
					<view class="parsed-item">
						<text class="parsed-label">类型：</text>
						<text class="parsed-value">{{parsedRecord.type === 'expense' ? '支出' : '收入'}}</text>
					</view>
					<view class="parsed-item">
						<text class="parsed-label">金额：</text>
						<text class="parsed-value">¥{{parsedRecord.amount}}</text>
					</view>
					<view v-if="parsedRecord.category" class="parsed-item">
						<text class="parsed-label">分类：</text>
						<text class="parsed-value">{{parsedRecord.category}}</text>
					</view>
					<view v-if="parsedRecord.date" class="parsed-item">
						<text class="parsed-label">日期：</text>
						<text class="parsed-value">{{parsedRecord.dateText || parsedRecord.date}}</text>
					</view>
					<view v-if="parsedRecord.note" class="parsed-item">
						<text class="parsed-label">备注：</text>
						<text class="parsed-value">{{parsedRecord.note}}</text>
					</view>
				</view>
				
				<view class="result-actions">
					<button class="retry-btn" @click="retryRecording">重新录音</button>
					<button class="save-btn" @click="saveRecord" :disabled="!parsedRecord">保存记录</button>
				</view>
			</view>
		</view>
		
		<!-- 使用说明 -->
		<view class="tips-section">
			<view class="tips-card">
				<view class="tips-header">
					<text class="tips-title">💡 使用说明</text>
				</view>
				<view class="tips-content">
					<text class="tip-item">• 支持自然语言：如"买菜花了30块钱"</text>
					<text class="tip-item">• 支持简洁表达：如"餐饮50"、"工资5000"</text>
					<text class="tip-item">• 支持时间表达：如"昨天买书花了80"</text>
					<text class="tip-item">• 录音时请保持安静，说话清晰</text>
				</view>
			</view>
		</view>
		
		<!-- 编辑弹窗 -->
		<view v-if="showEditPopup" class="popup-overlay" @click="closeEditPopup">
			<view class="popup-content" @click.stop>
				<view class="popup-header">
					<text class="popup-title">编辑识别结果</text>
					<text class="popup-close" @click="closeEditPopup">×</text>
				</view>
				
				<view class="form-section">
					<view class="form-item">
						<text class="form-label">识别文本</text>
						<textarea 
							class="form-textarea" 
							v-model="editText" 
							placeholder="请输入或修改识别文本"
							maxlength="200"
						></textarea>
					</view>
				</view>
				
				<view class="popup-actions">
					<button class="cancel-btn" @click="closeEditPopup">取消</button>
					<button class="confirm-btn" @click="confirmEdit">确认</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { createRecord } from '@/utils/models.js';
import StorageManager from '@/utils/storageManager.js';
import speechRecognitionCompatService from '@/utils/speechRecognitionCompat.js';

export default {
	data() {
		return {
			isRecording: false,
			isProcessing: false,
			voiceStatus: '点击开始录音',
			recognitionResult: '',
			parsedRecord: null,
			showEditPopup: false,
			editText: '',
			accounts: [],
			categories: [],
			accurateToday: null  // 存储variflight-mcp提供的准确今天日期
		};
	},
	
	onLoad() {
		this.loadData();
		this.checkCompatibility();
	},
	
	methods: {
		loadData() {
			this.accounts = StorageManager.getAccounts();
			this.categories = StorageManager.getCategories();
		},

		// 检查兼容性
		checkCompatibility() {
			const compatibility = speechRecognitionCompatService.checkCompatibility();
			console.log('兼容性检查结果:', compatibility);

			if (!compatibility.supported) {
				uni.showModal({
					title: '兼容性提示',
					content: `当前环境(${compatibility.platform})不支持语音识别功能：\n${compatibility.issues.join('\n')}`,
					showCancel: false
				});
			} else {
				console.log(`语音识别功能在${compatibility.platform}平台上可用`);
			}
		},
		
		goBack() {
			uni.navigateBack();
		},
		
		// 开始录音
		async startRecording() {
			try {
				this.isRecording = true;
				this.voiceStatus = '正在录音...';
				this.recognitionResult = '';
				this.parsedRecord = null;

				// 调用兼容的语音识别服务
				await speechRecognitionCompatService.startRecording();

			} catch (error) {
				console.error('开始录音失败:', error);
				this.isRecording = false;
				this.voiceStatus = '录音失败';

				let errorMessage = '录音失败';
				if (error.message.includes('Permission denied') || error.message.includes('权限')) {
					errorMessage = '请允许访问麦克风权限';
				} else if (error.message.includes('不支持') || error.message.includes('undefined')) {
					errorMessage = '当前环境不支持录音功能，请在支持的浏览器中使用';
				} else if (error.message.includes('网络')) {
					errorMessage = '网络连接失败，请检查网络';
				} else {
					errorMessage = error.message || '录音失败，请重试';
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				});
			}
		},
		
		// 停止录音
		async stopRecording() {
			try {
				this.isRecording = false;
				this.isProcessing = true;
				this.voiceStatus = '正在识别...';

				// 调用兼容的语音识别服务
				const result = await speechRecognitionCompatService.stopRecording();

				this.isProcessing = false;
				this.voiceStatus = '识别完成';

				// 处理识别结果（现在result可能是对象或字符串）
				let hasValidResult = false;
				let displayText = '';

				if (result) {
					if (typeof result === 'string') {
						// 旧版本：字符串格式
						hasValidResult = result.trim() !== '';
						displayText = result;
						this.recognitionResult = result;
					} else if (typeof result === 'object' && result.text) {
						// 新版本：对象格式
						hasValidResult = result.text.trim() !== '';
						displayText = result.text;
						this.recognitionResult = result.text;
					}
				}

				if (hasValidResult) {
					console.log('语音识别结果:', result);

					// 显示识别结果给用户
					uni.showToast({
						title: `识别结果: ${displayText}`,
						icon: 'none',
						duration: 2000
					});

					this.parseVoiceText(result);
				} else {
					this.voiceStatus = '未识别到内容';
					uni.showToast({
						title: '未识别到语音内容，请重试',
						icon: 'none'
					});
				}

			} catch (error) {
				console.error('语音识别失败:', error);
				this.isRecording = false;
				this.isProcessing = false;
				this.voiceStatus = '识别失败';

				let errorMessage = '语音识别失败';
				if (error.message.includes('无法连接uniCloud本地调试服务')) {
					errorMessage = '已切换到备用识别服务，功能正常';
					// 如果是uniCloud连接问题，不显示为错误，因为已经有备用方案
					this.voiceStatus = '请重新尝试录音';
					uni.showToast({
						title: errorMessage,
						icon: 'success',
						duration: 2000
					});
					return; // 不显示错误，让用户重新尝试
				} else if (error.message.includes('网络')) {
					errorMessage = '网络连接失败，请检查网络';
				} else if (error.message.includes('API')) {
					errorMessage = 'API调用失败，请稍后重试';
				} else if (error.message.includes('云函数')) {
					errorMessage = '语音识别服务暂时不可用，已启用备用服务';
				} else {
					errorMessage = error.message || '语音识别失败';
				}

				uni.showToast({
					title: errorMessage,
					icon: 'none',
					duration: 3000
				});
			}
		},

		// 取消录音
		cancelRecording() {
			try {
				speechRecognitionCompatService.cancelRecording();
				this.isRecording = false;
				this.isProcessing = false;
				this.voiceStatus = '已取消录音';

				uni.showToast({
					title: '已取消录音',
					icon: 'none'
				});

			} catch (error) {
				console.error('取消录音失败:', error);
			}
		},

		// 解析语音文本
		parseVoiceText(result) {
			try {
				let parsed = null;
				let accurateToday = null;

				// 如果result是字符串，说明是旧版本返回格式或手动编辑的文本
				if (typeof result === 'string') {
					const text = result;
					console.log('使用本地解析文本:', text);
					parsed = this.extractRecordInfo(text);
				} else if (result && typeof result === 'object') {
					// 新版本：云函数返回的对象格式（包含variflight-mcp日期信息）
					if (result.parsedInfo) {
						console.log('使用云函数解析结果:', result.parsedInfo);
						parsed = result.parsedInfo;

						// 获取variflight-mcp提供的准确日期信息
						if (result.accurateToday) {
							accurateToday = result.accurateToday;
							this.accurateToday = accurateToday;  // 存储到data中
							console.log('获取到variflight-mcp准确日期:', accurateToday);
						}
					} else if (result.text) {
						console.log('云函数返回文本，使用本地解析:', result.text);
						parsed = this.extractRecordInfo(result.text);
					}
				}

				if (parsed) {
					this.parsedRecord = parsed;
					console.log('最终解析结果:', parsed);

					// 显示日期识别信息
					if (parsed.date) {
						let dateMessage = `识别到日期: ${parsed.dateText || parsed.date}`;
						if (accurateToday) {
							dateMessage += ` (基于variflight-mcp准确日期: ${accurateToday})`;
						}
						uni.showToast({
							title: dateMessage,
							icon: 'none',
							duration: 3000
						});
					} else if (accurateToday) {
						// 没有识别到具体日期，但有准确的今天日期
						uni.showToast({
							title: `将使用当前日期: ${accurateToday}`,
							icon: 'none',
							duration: 2000
						});
					}
				} else {
					uni.showToast({
						title: '无法识别记账信息',
						icon: 'none'
					});
				}
			} catch (error) {
				console.error('解析语音文本失败:', error);
				uni.showToast({
					title: '解析失败',
					icon: 'none'
				});
			}
		},
		
		// 从文本中提取记账信息
		extractRecordInfo(text) {
			console.log('解析语音文本:', text);

			// 更灵活的金额匹配
			const amountPatterns = [
				/(\d+(?:\.\d+)?)[元块钱]/,           // 30元, 30块, 30钱
				/(\d+(?:\.\d+)?)块/,                 // 30块
				/(\d+(?:\.\d+)?)元/,                 // 30元
				/花了?(\d+(?:\.\d+)?)/,              // 花了30, 花30
				/(\d+(?:\.\d+)?)$/,                  // 纯数字结尾
				/(\d+(?:\.\d+)?)[费用]/,             // 30费用
				/收入(\d+(?:\.\d+)?)/,               // 收入5000
				/(\d+(?:\.\d+)?)收入/                // 5000收入
			];

			let amount = null;
			let amountMatch = null;

			for (const pattern of amountPatterns) {
				amountMatch = text.match(pattern);
				if (amountMatch) {
					amount = parseFloat(amountMatch[1]);
					console.log('匹配到金额:', amount, '使用模式:', pattern);
					break;
				}
			}

			if (!amount || amount <= 0) {
				console.log('未匹配到有效金额');
				return null;
			}

			// 提取日期时间信息
			const dateTimeInfo = this.extractDateTime(text);
			console.log('提取的日期时间信息:', dateTimeInfo);
			
			// 判断收支类型 - 更全面的关键词匹配
			const incomeKeywords = [
				'工资', '收入', '奖金', '红包', '转入', '薪水', '薪资',
				'分红', '利息', '退款', '报销', '补贴', '津贴', '提成',
				'兼职', '外快', '零花钱', '压岁钱', '礼金'
			];

			const isIncome = incomeKeywords.some(keyword => text.includes(keyword));
			const type = isIncome ? 'income' : 'expense';
			console.log('识别类型:', type, '原文:', text);

			// 更全面的分类匹配
			let category = null;
			let categoryId = null;

			const categoryKeywords = {
				'餐饮': [
					'吃饭', '午餐', '晚餐', '早餐', '买菜', '餐饮', '食物', '零食',
					'外卖', '快餐', '火锅', '烧烤', '咖啡', '奶茶', '饮料', '水果',
					'蔬菜', '肉类', '米面', '调料', '厨房', '做饭', '聚餐'
				],
				'交通': [
					'打车', '公交', '地铁', '出租车', '交通', '滴滴', '出行',
					'汽油', '加油', '停车', '过路费', '高速', '火车', '飞机',
					'机票', '车票', '船票', '摩托', '电动车', '自行车'
				],
				'购物': [
					'买', '购物', '商品', '衣服', '鞋子', '包包', '化妆品',
					'护肤', '洗发', '牙膏', '毛巾', '床单', '家具', '电器',
					'手机', '电脑', '数码', '文具', '书籍', '玩具'
				],
				'娱乐': [
					'电影', '游戏', '娱乐', 'KTV', '酒吧', '网吧', '台球',
					'健身', '游泳', '旅游', '景点', '门票', '演出', '音乐会',
					'体育', '运动', '球类', '户外', '爬山', '钓鱼'
				],
				'生活': [
					'电费', '水费', '房租', '生活', '物业', '网费', '话费',
					'燃气', '暖气', '维修', '清洁', '洗衣', '理发', '美容',
					'医疗', '药品', '看病', '体检', '保险', '银行', '手续费'
				],
				'工资': ['工资', '薪水', '薪资', '底薪', '基本工资'],
				'奖金': ['奖金', '红包', '分红', '提成', '津贴', '补贴', '年终奖'],
				'其他收入': ['利息', '退款', '报销', '兼职', '外快', '礼金']
			};
			
			// 分类匹配 - 按优先级匹配
			for (const [cat, keywords] of Object.entries(categoryKeywords)) {
				const matchedKeyword = keywords.find(keyword => text.includes(keyword));
				if (matchedKeyword) {
					category = cat;
					console.log('匹配到分类:', cat, '关键词:', matchedKeyword);

					// 查找对应的分类ID
					const foundCategory = this.categories.find(c => c.name === cat);
					if (foundCategory) {
						categoryId = foundCategory.id;
					}
					break;
				}
			}

			// 如果没有匹配到分类，根据收支类型设置默认分类
			if (!category) {
				if (type === 'income') {
					category = '其他收入';
				} else {
					category = '其他支出';
				}
				console.log('使用默认分类:', category);
			}

			// 智能提取备注
			let note = text;

			// 移除金额相关文字
			const amountTexts = ['元', '块', '钱', '花了', '费用', '收入'];
			amountTexts.forEach(txt => {
				note = note.replace(new RegExp(txt, 'g'), '');
			});

			// 移除数字
			note = note.replace(/\d+(?:\.\d+)?/g, '');

			// 移除匹配到的分类关键词
			if (category && categoryKeywords[category]) {
				categoryKeywords[category].forEach(keyword => {
					note = note.replace(new RegExp(keyword, 'g'), '');
				});
			}

			// 移除日期时间相关文字
			if (dateTimeInfo.dateText) {
				note = note.replace(dateTimeInfo.dateText, '');
			}
			if (dateTimeInfo.timeText) {
				note = note.replace(dateTimeInfo.timeText, '');
			}

			// 清理多余的字符和空格
			note = note.replace(/[的了]/g, '').trim();

			// 如果备注为空，使用原始文本
			if (!note) {
				note = text;
			}

			const result = {
				type,
				amount,
				category,
				categoryId,
				note,
				date: dateTimeInfo.date,
				time: dateTimeInfo.time,
				dateText: dateTimeInfo.dateText,
				timeText: dateTimeInfo.timeText
			};

			console.log('解析结果:', result);
			return result;
		},
		
		// 编辑识别结果
		editResult() {
			this.editText = this.recognitionResult;
			this.showEditPopup = true;
		},
		
		// 关闭编辑弹窗
		closeEditPopup() {
			this.showEditPopup = false;
			this.editText = '';
		},
		
		// 确认编辑
		confirmEdit() {
			if (this.editText.trim()) {
				this.recognitionResult = this.editText.trim();
				this.parseVoiceText(this.recognitionResult);
				this.closeEditPopup();
			}
		},
		
		// 重新录音
		retryRecording() {
			this.recognitionResult = '';
			this.parsedRecord = null;
			this.voiceStatus = '点击开始录音';
		},
		
		// 保存记录
		async saveRecord() {
			if (!this.parsedRecord) {
				uni.showToast({
					title: '没有可保存的记录',
					icon: 'none'
				});
				return;
			}
			
			try {
				// 使用默认账户（现金）
				const defaultAccount = this.accounts.find(a => a.type === 'cash') || this.accounts[0];
				
				// 处理日期：优先使用识别的日期，其次使用variflight-mcp准确日期，最后使用当前时间
				let recordDate;
				if (this.parsedRecord.date) {
					// 将识别到的日期字符串转换为时间戳
					recordDate = new Date(this.parsedRecord.date).getTime();
					console.log('使用识别的日期:', this.parsedRecord.date, '时间戳:', recordDate);
				} else if (this.accurateToday) {
					// 使用variflight-mcp提供的准确今天日期
					recordDate = new Date(this.accurateToday).getTime();
					console.log('使用variflight-mcp准确日期:', this.accurateToday, '时间戳:', recordDate);
				} else {
					// 降级方案：使用当前时间
					recordDate = Date.now();
					console.log('使用当前时间（降级方案）:', new Date(recordDate).toISOString());
				}

				const record = createRecord({
					type: this.parsedRecord.type,
					categoryId: this.parsedRecord.categoryId || '',
					accountId: defaultAccount?.id || '',
					amount: this.parsedRecord.amount,
					note: this.parsedRecord.note,
					date: recordDate
				});
				
				// 保存记录
				const records = StorageManager.getRecords();
				records.unshift(record);
				StorageManager.saveRecords(records);
				
				// 更新账户余额
				if (defaultAccount) {
					const accounts = StorageManager.getAccounts();
					const accountIndex = accounts.findIndex(a => a.id === defaultAccount.id);
					if (accountIndex !== -1) {
						if (this.parsedRecord.type === 'expense') {
							accounts[accountIndex].balance -= this.parsedRecord.amount;
						} else {
							accounts[accountIndex].balance += this.parsedRecord.amount;
						}
						StorageManager.saveAccounts(accounts);
					}
				}
				
				uni.showToast({
					title: '保存成功',
					icon: 'success'
				});
				
				// 延迟返回
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
				
			} catch (error) {
				console.error('保存记录失败:', error);
				uni.showToast({
					title: '保存失败',
					icon: 'none'
				});
			}
		}
	}
};
</script>

<style scoped>
.page {
	background-color: #f5f5f5;
	min-height: 100vh;
}

/* 头部样式 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 60rpx 40rpx 40rpx;
	color: white;
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.back-btn {
	font-size: 40rpx;
	font-weight: bold;
}

.header-title {
	font-size: 36rpx;
	font-weight: bold;
}

/* 语音识别区域 */
.voice-section {
	padding: 80rpx 40rpx;
	text-align: center;
}

.voice-container {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.voice-circle {
	width: 200rpx;
	height: 200rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	margin-bottom: 40rpx;
	transition: all 0.3s;
	box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
}

.voice-circle.recording {
	animation: pulse 1s infinite;
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
}

.voice-circle.processing {
	animation: rotate 2s linear infinite;
	background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
}

@keyframes pulse {
	0% { transform: scale(1); }
	50% { transform: scale(1.1); }
	100% { transform: scale(1); }
}

@keyframes rotate {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.voice-icon {
	font-size: 60rpx;
	color: white;
}

.voice-status {
	font-size: 32rpx;
	color: #333;
	margin-bottom: 40rpx;
}

.voice-controls {
	display: flex;
	gap: 20rpx;
}

.record-btn, .stop-btn, .cancel-btn {
	padding: 24rpx 48rpx;
	border-radius: 50rpx;
	font-size: 28rpx;
	font-weight: bold;
	border: none;
	margin: 0 10rpx;
}

.record-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.stop-btn {
	background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
	color: white;
}

.cancel-btn {
	background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
	color: white;
}

/* 识别结果 */
.result-section {
	margin: 0 40rpx 40rpx;
}

.result-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.result-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 30rpx;
}

.result-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.result-edit {
	font-size: 28rpx;
	color: #667eea;
}

.result-content {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 30rpx;
	margin-bottom: 30rpx;
}

.result-text {
	font-size: 30rpx;
	color: #333;
	line-height: 1.5;
}

.parsed-info {
	border-top: 1rpx solid #f0f0f0;
	padding-top: 30rpx;
	margin-bottom: 30rpx;
}

.parsed-item {
	display: flex;
	margin-bottom: 20rpx;
}

.parsed-label {
	font-size: 28rpx;
	color: #666;
	width: 120rpx;
}

.parsed-value {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
}

.result-actions {
	display: flex;
	gap: 20rpx;
}

.retry-btn, .save-btn {
	flex: 1;
	height: 80rpx;
	border-radius: 20rpx;
	font-size: 28rpx;
	font-weight: bold;
	border: none;
}

.retry-btn {
	background: #f8f9fa;
	color: #666;
}

.save-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

.save-btn:disabled {
	background: #ccc;
	color: #999;
}

/* 使用说明 */
.tips-section {
	margin: 0 40rpx;
}

.tips-card {
	background: white;
	border-radius: 20rpx;
	padding: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.tips-header {
	margin-bottom: 30rpx;
}

.tips-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
}

.tips-content {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.tip-item {
	font-size: 26rpx;
	color: #666;
	line-height: 1.5;
}

/* 弹窗样式 */
.popup-overlay {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.5);
	z-index: 1000;
	display: flex;
	align-items: flex-end;
}

.popup-content {
	background: white;
	border-radius: 20rpx 20rpx 0 0;
	padding: 40rpx;
	width: 100%;
	max-height: 80vh;
}

.popup-header {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-bottom: 40rpx;
}

.popup-title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.popup-close {
	font-size: 48rpx;
	color: #999;
	font-weight: bold;
}

.form-section {
	margin-bottom: 40rpx;
}

.form-item {
	margin-bottom: 40rpx;
}

.form-label {
	font-size: 28rpx;
	color: #333;
	font-weight: 500;
	display: block;
	margin-bottom: 20rpx;
}

.form-textarea {
	width: 100%;
	min-height: 200rpx;
	padding: 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border: none;
	font-size: 28rpx;
	color: #333;
	line-height: 1.5;
}

.popup-actions {
	display: flex;
	gap: 20rpx;
}

.cancel-btn, .confirm-btn {
	flex: 1;
	height: 88rpx;
	border-radius: 20rpx;
	font-size: 32rpx;
	font-weight: bold;
	border: none;
}

.cancel-btn {
	background: #f8f9fa;
	color: #666;
}

.confirm-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}
</style>
