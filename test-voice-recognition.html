<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音识别测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #0056CC;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .recording {
            background: #FF3B30 !important;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f0f0f0;
            border-left: 4px solid #007AFF;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #e8f5e8;
            border-left: 4px solid #28a745;
        }
        .error {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #ffe6e6;
            border-left: 4px solid #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 语音识别功能测试</h1>
        <p>测试硅基流动API直接调用功能</p>
        
        <div>
            <button id="recordBtn" class="button">开始录音</button>
            <button id="clearBtn" class="button">清除日志</button>
        </div>
        
        <div id="status" class="status">准备就绪</div>
        <div id="result" style="display: none;"></div>
        <div id="error" style="display: none;"></div>
        
        <h3>📋 执行日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        // 简化的语音识别测试类
        class VoiceRecognitionTest {
            constructor() {
                this.isRecording = false;
                this.mediaRecorder = null;
                this.audioChunks = [];
                
                this.recordBtn = document.getElementById('recordBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.statusDiv = document.getElementById('status');
                this.resultDiv = document.getElementById('result');
                this.errorDiv = document.getElementById('error');
                this.logDiv = document.getElementById('log');
                
                this.bindEvents();
            }
            
            bindEvents() {
                this.recordBtn.addEventListener('click', () => {
                    if (this.isRecording) {
                        this.stopRecording();
                    } else {
                        this.startRecording();
                    }
                });
                
                this.clearBtn.addEventListener('click', () => {
                    this.logDiv.textContent = '';
                });
            }
            
            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.logDiv.textContent += `[${timestamp}] ${message}\n`;
                this.logDiv.scrollTop = this.logDiv.scrollHeight;
                console.log(message);
            }
            
            updateStatus(message) {
                this.statusDiv.textContent = message;
                this.log(`状态: ${message}`);
            }
            
            showResult(data) {
                this.resultDiv.style.display = 'block';
                this.errorDiv.style.display = 'none';
                this.resultDiv.className = 'result';
                this.resultDiv.innerHTML = `
                    <h4>🎉 识别成功</h4>
                    <p><strong>识别文本:</strong> ${data.text}</p>
                    <p><strong>语言:</strong> ${data.language}</p>
                    <p><strong>时长:</strong> ${data.duration}秒</p>
                    ${data.parsedInfo ? `
                        <h5>📊 解析结果</h5>
                        <p><strong>类型:</strong> ${data.parsedInfo.type}</p>
                        <p><strong>金额:</strong> ${data.parsedInfo.amount}</p>
                        <p><strong>分类:</strong> ${data.parsedInfo.category}</p>
                        <p><strong>描述:</strong> ${data.parsedInfo.description}</p>
                        <p><strong>日期:</strong> ${data.parsedInfo.date}</p>
                    ` : ''}
                `;
            }
            
            showError(message) {
                this.errorDiv.style.display = 'block';
                this.resultDiv.style.display = 'none';
                this.errorDiv.className = 'error';
                this.errorDiv.innerHTML = `<h4>❌ 错误</h4><p>${message}</p>`;
            }
            
            async startRecording() {
                try {
                    this.log('请求麦克风权限...');
                    const stream = await navigator.mediaDevices.getUserMedia({ 
                        audio: {
                            sampleRate: 16000,
                            channelCount: 1,
                            echoCancellation: true,
                            noiseSuppression: true
                        } 
                    });
                    
                    this.log('麦克风权限获取成功');
                    
                    // 选择支持的MIME类型
                    const mimeTypes = [
                        'audio/webm;codecs=opus',
                        'audio/webm',
                        'audio/mp4',
                        'audio/wav'
                    ];
                    
                    let selectedMimeType = null;
                    for (const mimeType of mimeTypes) {
                        if (MediaRecorder.isTypeSupported(mimeType)) {
                            selectedMimeType = mimeType;
                            break;
                        }
                    }
                    
                    if (!selectedMimeType) {
                        throw new Error('浏览器不支持音频录制');
                    }
                    
                    this.log(`使用MIME类型: ${selectedMimeType}`);
                    
                    this.mediaRecorder = new MediaRecorder(stream, {
                        mimeType: selectedMimeType
                    });
                    
                    this.audioChunks = [];
                    
                    this.mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            this.audioChunks.push(event.data);
                        }
                    };
                    
                    this.mediaRecorder.onstop = () => {
                        this.processAudio();
                    };
                    
                    this.mediaRecorder.start();
                    this.isRecording = true;
                    
                    this.recordBtn.textContent = '停止录音';
                    this.recordBtn.classList.add('recording');
                    this.updateStatus('正在录音...');
                    
                } catch (error) {
                    this.log(`录音启动失败: ${error.message}`);
                    this.showError(`录音启动失败: ${error.message}`);
                }
            }
            
            stopRecording() {
                if (this.mediaRecorder && this.isRecording) {
                    this.mediaRecorder.stop();
                    this.mediaRecorder.stream.getTracks().forEach(track => track.stop());
                    
                    this.isRecording = false;
                    this.recordBtn.textContent = '开始录音';
                    this.recordBtn.classList.remove('recording');
                    this.updateStatus('录音已停止，正在处理...');
                }
            }
            
            async processAudio() {
                try {
                    this.log('开始处理音频数据...');
                    
                    const audioBlob = new Blob(this.audioChunks, { 
                        type: this.mediaRecorder.mimeType 
                    });
                    
                    this.log(`音频Blob大小: ${audioBlob.size} bytes`);
                    this.log(`音频类型: ${audioBlob.type}`);
                    
                    // 转换为base64
                    const base64Data = await this.blobToBase64(audioBlob);
                    this.log(`Base64数据长度: ${base64Data.length}`);
                    
                    // 调用语音识别API
                    const result = await this.directApiCall(base64Data, 'webm');
                    
                    this.log('语音识别完成');
                    this.showResult(result);
                    this.updateStatus('识别完成');
                    
                } catch (error) {
                    this.log(`音频处理失败: ${error.message}`);
                    this.showError(`音频处理失败: ${error.message}`);
                    this.updateStatus('处理失败');
                }
            }
            
            // 这里复制修复后的directApiCall方法
            async directApiCall(base64Data, audioFormat) {
                try {
                    this.log('使用直接API调用模式进行语音识别');
                    this.log(`音频格式: ${audioFormat}, 数据长度: ${base64Data.length}`);

                    // 硅基流动API配置
                    const API_KEY = 'sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl';
                    const API_URL = 'https://api.siliconflow.cn/v1/audio/transcriptions';

                    // 移除base64前缀（如果存在）
                    const cleanBase64 = base64Data.replace(/^data:audio\/[^;]+;base64,/, '');
                    
                    // 将base64转换为二进制数据
                    const audioBuffer = this.base64ToArrayBuffer(cleanBase64);
                    this.log(`音频数据大小: ${audioBuffer.byteLength} bytes`);

                    // 构建multipart/form-data请求
                    const boundary = '----formdata-' + this.generateRandomString(32);
                    
                    // 构建请求体
                    const formData = this.buildMultipartFormData(audioBuffer, audioFormat, boundary);
                    
                    this.log('准备发送请求到硅基流动API');
                    
                    // 发送请求
                    const response = await fetch(API_URL, {
                        method: 'POST',
                        headers: {
                            'Authorization': `Bearer ${API_KEY}`,
                            'Content-Type': `multipart/form-data; boundary=${boundary}`,
                            'Content-Length': formData.byteLength.toString()
                        },
                        body: formData
                    });

                    if (!response.ok) {
                        const errorText = await response.text();
                        throw new Error(`HTTP ${response.status}: ${errorText}`);
                    }

                    const result = await response.json();
                    this.log(`硅基流动API响应: ${JSON.stringify(result)}`);

                    if (!result.text) {
                        throw new Error('API返回数据格式错误');
                    }

                    this.log(`语音识别成功: ${result.text}`);

                    // 获取准确的今天日期
                    const accurateToday = new Date().toISOString().split('T')[0];

                    // 解析语音文本，提取记账信息
                    const parsedInfo = this.parseRecordInfo(result.text, accurateToday);

                    return {
                        text: result.text,
                        duration: result.duration || 0,
                        language: result.language || 'zh',
                        parsedInfo: parsedInfo,
                        accurateToday: accurateToday
                    };

                } catch (error) {
                    this.log(`直接API调用失败: ${error.message}`);
                    throw new Error('语音识别API调用失败: ' + error.message);
                }
            }
            
            // 辅助方法
            blobToBase64(blob) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result);
                    reader.onerror = reject;
                    reader.readAsDataURL(blob);
                });
            }
            
            base64ToArrayBuffer(base64) {
                const binaryString = atob(base64);
                const bytes = new Uint8Array(binaryString.length);
                for (let i = 0; i < binaryString.length; i++) {
                    bytes[i] = binaryString.charCodeAt(i);
                }
                return bytes.buffer;
            }
            
            generateRandomString(length) {
                const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
                let result = '';
                for (let i = 0; i < length; i++) {
                    result += chars.charAt(Math.floor(Math.random() * chars.length));
                }
                return result;
            }
            
            buildMultipartFormData(audioBuffer, audioFormat, boundary) {
                const encoder = new TextEncoder();
                
                const filePart = encoder.encode(
                    `--${boundary}\r\n` +
                    `Content-Disposition: form-data; name="file"; filename="audio.${audioFormat}"\r\n` +
                    `Content-Type: audio/${audioFormat}\r\n\r\n`
                );
                
                const modelPart = encoder.encode(
                    `\r\n--${boundary}\r\n` +
                    `Content-Disposition: form-data; name="model"\r\n\r\n` +
                    `FunAudioLLM/SenseVoiceSmall\r\n`
                );
                
                const languagePart = encoder.encode(
                    `--${boundary}\r\n` +
                    `Content-Disposition: form-data; name="language"\r\n\r\n` +
                    `zh\r\n`
                );
                
                const endPart = encoder.encode(`--${boundary}--\r\n`);
                
                const totalLength = filePart.byteLength + audioBuffer.byteLength + 
                                   modelPart.byteLength + languagePart.byteLength + endPart.byteLength;
                
                const result = new Uint8Array(totalLength);
                let offset = 0;
                
                result.set(new Uint8Array(filePart), offset);
                offset += filePart.byteLength;
                
                result.set(new Uint8Array(audioBuffer), offset);
                offset += audioBuffer.byteLength;
                
                result.set(new Uint8Array(modelPart), offset);
                offset += modelPart.byteLength;
                
                result.set(new Uint8Array(languagePart), offset);
                offset += languagePart.byteLength;
                
                result.set(new Uint8Array(endPart), offset);
                
                return result.buffer;
            }
            
            parseRecordInfo(text, accurateToday) {
                this.log(`解析语音文本: ${text}`);

                // 金额匹配
                const amountPatterns = [
                    /(\d+(?:\.\d+)?)[元块钱]/,
                    /(\d+(?:\.\d+)?)块/,
                    /(\d+(?:\.\d+)?)元/,
                    /花了?(\d+(?:\.\d+)?)/,
                    /(\d+(?:\.\d+)?)$/,
                    /收入(\d+(?:\.\d+)?)/,
                    /(\d+(?:\.\d+)?)收入/
                ];

                let amount = null;
                for (const pattern of amountPatterns) {
                    const match = text.match(pattern);
                    if (match) {
                        amount = parseFloat(match[1]);
                        this.log(`匹配到金额: ${amount}`);
                        break;
                    }
                }

                if (!amount || amount <= 0) {
                    this.log('未匹配到有效金额');
                    return null;
                }

                // 收支类型识别
                const incomeKeywords = ['工资', '收入', '奖金', '红包', '薪水', '薪资'];
                const isIncome = incomeKeywords.some(keyword => text.includes(keyword));
                const type = isIncome ? '收入' : '支出';

                // 简单的分类识别
                let category = '其他';
                if (isIncome) {
                    category = '工资';
                } else {
                    const categoryMap = {
                        '餐饮': ['吃', '餐', '饭', '菜', '食', '喝', '咖啡', '奶茶'],
                        '交通': ['车', '油', '停车', '地铁', '公交', '打车', '滴滴'],
                        '购物': ['买', '购', '商场', '超市', '淘宝', '京东'],
                        '娱乐': ['电影', '游戏', 'KTV', '娱乐', '玩'],
                        '生活': ['水电', '房租', '物业', '话费', '网费']
                    };

                    for (const [cat, keywords] of Object.entries(categoryMap)) {
                        if (keywords.some(keyword => text.includes(keyword))) {
                            category = cat;
                            break;
                        }
                    }
                }

                return {
                    type: type,
                    amount: amount,
                    category: category,
                    description: text,
                    date: accurateToday,
                    categoryId: '',
                    note: text
                };
            }
        }
        
        // 初始化测试
        document.addEventListener('DOMContentLoaded', () => {
            new VoiceRecognitionTest();
        });
    </script>
</body>
</html>
