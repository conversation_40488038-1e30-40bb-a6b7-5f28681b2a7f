/**
 * 硅基流动API连接测试脚本
 * 用于验证API密钥和端点的有效性
 */

const https = require('https');
const crypto = require('crypto');

// 硅基流动API配置
const API_KEY = 'sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl';
const API_URL = 'https://api.siliconflow.cn/v1/audio/transcriptions';

/**
 * 测试API连接
 */
async function testApiConnection() {
    console.log('🔍 开始测试硅基流动API连接...');
    console.log('API端点:', API_URL);
    console.log('API密钥:', API_KEY.substring(0, 10) + '...');

    try {
        // 创建一个简单的测试音频数据（模拟WAV文件头）
        const testAudioData = createTestAudioBuffer();
        
        // 构建multipart/form-data请求
        const boundary = '----formdata-' + crypto.randomBytes(16).toString('hex');
        
        let formData = '';
        formData += `--${boundary}\r\n`;
        formData += `Content-Disposition: form-data; name="file"; filename="test.wav"\r\n`;
        formData += `Content-Type: audio/wav\r\n\r\n`;
        
        const textParts = formData;
        const textParts2 = `\r\n--${boundary}--\r\n`;
        
        // 创建完整的请求体
        const textBuffer1 = Buffer.from(textParts, 'utf8');
        const textBuffer2 = Buffer.from(textParts2, 'utf8');
        const requestBody = Buffer.concat([textBuffer1, testAudioData, textBuffer2]);
        
        console.log('📦 请求体大小:', requestBody.length, 'bytes');
        
        // 发送HTTP请求
        const response = await makeHttpRequest(API_URL, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': `multipart/form-data; boundary=${boundary}`,
                'Content-Length': requestBody.length.toString()
            }
        }, requestBody);
        
        console.log('✅ API响应状态:', response.statusCode);
        console.log('📄 响应头:', response.headers);
        console.log('📝 响应数据:', response.data);
        
        if (response.statusCode === 200) {
            console.log('🎉 API连接测试成功！');
            return true;
        } else if (response.statusCode === 401) {
            console.log('❌ API密钥无效或已过期');
            return false;
        } else if (response.statusCode === 400) {
            console.log('⚠️ 请求格式错误，但API端点可访问');
            return true; // 端点可访问，只是请求格式问题
        } else {
            console.log('❌ API请求失败，状态码:', response.statusCode);
            return false;
        }
        
    } catch (error) {
        console.error('❌ API连接测试失败:', error.message);
        
        if (error.code === 'ENOTFOUND') {
            console.log('🌐 DNS解析失败，请检查网络连接');
        } else if (error.code === 'ECONNREFUSED') {
            console.log('🚫 连接被拒绝，API服务可能不可用');
        } else if (error.code === 'ETIMEDOUT') {
            console.log('⏰ 连接超时，请检查网络状况');
        }
        
        return false;
    }
}

/**
 * 创建测试用的音频数据
 */
function createTestAudioBuffer() {
    // 创建一个简单的WAV文件头（44字节）+ 少量音频数据
    const wavHeader = Buffer.alloc(44);
    
    // WAV文件头
    wavHeader.write('RIFF', 0);
    wavHeader.writeUInt32LE(36, 4);
    wavHeader.write('WAVE', 8);
    wavHeader.write('fmt ', 12);
    wavHeader.writeUInt32LE(16, 16);
    wavHeader.writeUInt16LE(1, 20);  // PCM格式
    wavHeader.writeUInt16LE(1, 22);  // 单声道
    wavHeader.writeUInt32LE(16000, 24); // 采样率16kHz
    wavHeader.writeUInt32LE(32000, 28); // 字节率
    wavHeader.writeUInt16LE(2, 32);  // 块对齐
    wavHeader.writeUInt16LE(16, 34); // 位深度
    wavHeader.write('data', 36);
    wavHeader.writeUInt32LE(0, 40);
    
    // 添加一些测试音频数据（静音）
    const audioData = Buffer.alloc(1600); // 0.1秒的静音数据
    
    return Buffer.concat([wavHeader, audioData]);
}

/**
 * 发送HTTP请求
 */
function makeHttpRequest(url, options, data) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || 443,
            path: urlObj.pathname,
            method: options.method,
            headers: options.headers,
            timeout: 30000
        };
        
        const req = https.request(requestOptions, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsedData = JSON.parse(responseData);
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: parsedData
                    });
                } catch (e) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: responseData
                    });
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (data) {
            req.write(data);
        }
        
        req.end();
    });
}

/**
 * 测试网络连接
 */
async function testNetworkConnection() {
    console.log('🌐 测试网络连接...');
    
    try {
        const response = await makeHttpRequest('https://www.baidu.com', {
            method: 'GET',
            headers: {}
        });
        
        if (response.statusCode === 200) {
            console.log('✅ 网络连接正常');
            return true;
        } else {
            console.log('⚠️ 网络连接异常，状态码:', response.statusCode);
            return false;
        }
    } catch (error) {
        console.log('❌ 网络连接失败:', error.message);
        return false;
    }
}

// 主测试函数
async function main() {
    console.log('🚀 开始硅基流动API诊断测试\n');
    
    // 1. 测试网络连接
    const networkOk = await testNetworkConnection();
    console.log('');
    
    if (!networkOk) {
        console.log('❌ 网络连接失败，无法继续测试');
        return;
    }
    
    // 2. 测试API连接
    const apiOk = await testApiConnection();
    console.log('');
    
    // 3. 输出诊断结果
    console.log('📊 诊断结果总结:');
    console.log('- 网络连接:', networkOk ? '✅ 正常' : '❌ 异常');
    console.log('- API连接:', apiOk ? '✅ 正常' : '❌ 异常');
    
    if (networkOk && apiOk) {
        console.log('\n🎉 硅基流动API集成状态良好，可以正常使用！');
    } else {
        console.log('\n⚠️ 发现问题，需要进一步排查');
    }
}

// 运行测试
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    testApiConnection,
    testNetworkConnection
};
