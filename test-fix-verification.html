<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复验证测试</h1>
        <p>验证语音识别兼容性修复是否成功解决了H5环境中的uni.getFileSystemManager错误</p>
        
        <button class="test-button" onclick="testEnvironmentDetection()">测试环境检测</button>
        <button class="test-button" onclick="testFileSystemCalls()">测试文件系统调用</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
        
        <div id="status" class="status info">准备测试</div>
        
        <h3>📋 测试日志</h3>
        <div id="log" class="log">点击测试按钮开始验证修复效果...</div>
        
        <div class="info-box" style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 4px;">
            <h3>🎯 测试目标</h3>
            <ul>
                <li><strong>环境检测</strong>：确认能正确识别H5环境</li>
                <li><strong>API保护</strong>：确认不会调用uni.getFileSystemManager</li>
                <li><strong>错误处理</strong>：确认有合适的降级处理</li>
                <li><strong>功能完整</strong>：确认基本功能仍然可用</li>
            </ul>
        </div>
    </div>

    <script type="module">
        // 模拟config对象
        const config = {
            siliconFlow: {
                apiUrl: 'https://api.siliconflow.cn/v1/audio/transcriptions',
                apiKey: 'test-key',
                model: 'FunAudioLLM/SenseVoiceSmall'
            }
        };

        // 简化版的SpeechRecognitionCompatService用于测试
        class TestSpeechRecognitionCompatService {
            constructor() {
                this.apiUrl = config.siliconFlow.apiUrl;
                this.apiKey = config.siliconFlow.apiKey;
                this.model = config.siliconFlow.model;
                
                this.isRecording = false;
                this.recordingStartTime = null;
                
                // 检测当前平台
                this.platform = this.detectPlatform();
                
                // 全局运行时检测，确保H5环境安全
                this.isH5Environment = this.platform === 'H5' || typeof window !== 'undefined';
                this.hasUniFileSystem = typeof uni !== 'undefined' && uni.getFileSystemManager;
                this.hasUniUpload = typeof uni !== 'undefined' && uni.uploadFile;
                
                this.log('当前平台: ' + this.platform);
                this.log('H5环境: ' + this.isH5Environment);
                this.log('uni文件系统可用: ' + this.hasUniFileSystem);
                this.log('uni上传功能可用: ' + this.hasUniUpload);
            }

            detectPlatform() {
                if (typeof window !== 'undefined' && window.location) {
                    return 'H5';
                } else if (typeof wx !== 'undefined' && wx.miniProgram) {
                    return 'MP-WEIXIN';
                } else if (typeof plus !== 'undefined') {
                    return 'APP-PLUS';
                } else {
                    return 'UNKNOWN';
                }
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logElement = document.getElementById('log');
                logElement.textContent += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
                console.log(message);
            }

            updateStatus(message, type) {
                const statusElement = document.getElementById('status');
                statusElement.textContent = message;
                statusElement.className = `status ${type}`;
            }

            // 测试文件路径转base64（模拟修复后的逻辑）
            async fileToBase64(filePath) {
                return new Promise((resolve, reject) => {
                    this.log(`尝试转换文件为base64: ${filePath}`);
                    
                    // 使用全局运行时检测
                    if (this.isH5Environment || !this.hasUniFileSystem) {
                        this.log('✅ 检测到H5环境或uni文件系统不可用，跳过uni.getFileSystemManager调用');
                        
                        // H5环境，如果传入的已经是base64数据，直接返回
                        if (filePath.startsWith('data:')) {
                            this.log('✅ 检测到base64数据，直接返回');
                            resolve(filePath);
                            return;
                        }
                        // H5环境不支持文件路径读取，返回错误
                        this.log('⚠️ H5环境不支持文件路径读取，返回预期错误');
                        reject(new Error('H5环境不支持文件路径读取'));
                        return;
                    }

                    // 其他环境的处理逻辑
                    this.log('其他环境，会尝试使用uni.getFileSystemManager');
                    resolve('模拟的base64数据');
                });
            }

            // 测试保存base64到临时文件（模拟修复后的逻辑）
            async saveBase64ToTempFile(base64Data, audioFormat) {
                return new Promise((resolve, reject) => {
                    this.log(`尝试保存临时文件，格式: ${audioFormat}`);
                    
                    // 使用全局运行时检测
                    if (this.isH5Environment || !this.hasUniFileSystem) {
                        this.log('✅ 检测到H5环境或uni文件系统不可用，跳过文件系统操作');
                        this.log('✅ 直接返回base64数据');
                        resolve(base64Data);
                        return;
                    }

                    // 其他环境的处理逻辑
                    this.log('其他环境，会尝试使用文件系统管理器');
                    resolve('模拟的临时文件路径');
                });
            }

            // 测试上传文件调用（模拟修复后的逻辑）
            async callApiWithUploadFile(base64Data, audioFormat, apiKey, apiUrl) {
                try {
                    this.log('尝试使用uni.uploadFile调用API');
                    
                    // 使用全局运行时检测
                    if (this.isH5Environment || !this.hasUniUpload) {
                        this.log('✅ 检测到H5环境或uni上传功能不可用，跳过uni.uploadFile调用');
                        throw new Error('H5环境不支持uni.uploadFile');
                    }

                    this.log('其他环境，会尝试使用uni.uploadFile');
                    return { text: '模拟的识别结果' };
                } catch (error) {
                    this.log(`预期的错误: ${error.message}`);
                    throw error;
                }
            }
        }

        let speechService;

        window.testEnvironmentDetection = function() {
            const logElement = document.getElementById('log');
            logElement.textContent = '';
            
            try {
                speechService = new TestSpeechRecognitionCompatService();
                
                // 验证环境检测结果
                if (speechService.platform === 'H5' && speechService.isH5Environment && !speechService.hasUniFileSystem) {
                    speechService.log('🎉 环境检测正确！');
                    speechService.log('✅ 平台识别为H5');
                    speechService.log('✅ H5环境标志为true');
                    speechService.log('✅ uni文件系统标志为false');
                    speechService.updateStatus('环境检测测试通过', 'success');
                } else {
                    speechService.log('❌ 环境检测异常');
                    speechService.updateStatus('环境检测测试失败', 'error');
                }
                
            } catch (error) {
                console.error('环境检测测试失败:', error);
                document.getElementById('log').textContent += `❌ 环境检测失败: ${error.message}\n`;
                document.getElementById('status').textContent = '环境检测测试失败';
                document.getElementById('status').className = 'status error';
            }
        };

        window.testFileSystemCalls = async function() {
            if (!speechService) {
                speechService = new TestSpeechRecognitionCompatService();
            }

            try {
                speechService.log('\n=== 测试文件系统相关调用 ===');
                
                // 测试fileToBase64
                speechService.log('\n1. 测试fileToBase64方法');
                try {
                    const result1 = await speechService.fileToBase64('data:audio/mp3;base64,test');
                    speechService.log(`✅ base64数据处理成功: ${result1.substring(0, 30)}...`);
                } catch (error) {
                    speechService.log(`✅ 预期的错误: ${error.message}`);
                }

                try {
                    await speechService.fileToBase64('/path/to/file.mp3');
                } catch (error) {
                    speechService.log(`✅ 预期的错误: ${error.message}`);
                }

                // 测试saveBase64ToTempFile
                speechService.log('\n2. 测试saveBase64ToTempFile方法');
                const result2 = await speechService.saveBase64ToTempFile('test-base64-data', 'mp3');
                speechService.log(`✅ 保存临时文件成功: ${result2}`);

                // 测试callApiWithUploadFile
                speechService.log('\n3. 测试callApiWithUploadFile方法');
                try {
                    await speechService.callApiWithUploadFile('test-data', 'mp3', 'test-key', 'test-url');
                } catch (error) {
                    speechService.log(`✅ 预期的错误: ${error.message}`);
                }

                speechService.log('\n🎉 所有文件系统调用测试通过！');
                speechService.log('✅ 没有调用uni.getFileSystemManager');
                speechService.log('✅ 没有调用uni.uploadFile');
                speechService.log('✅ 所有H5环境检测都正常工作');
                
                speechService.updateStatus('文件系统调用测试通过', 'success');

            } catch (error) {
                speechService.log(`❌ 文件系统调用测试失败: ${error.message}`);
                speechService.updateStatus('文件系统调用测试失败', 'error');
            }
        };

        window.clearLog = function() {
            document.getElementById('log').textContent = '';
            document.getElementById('status').textContent = '日志已清空';
            document.getElementById('status').className = 'status info';
        };

        // 页面加载时自动运行测试
        window.onload = function() {
            testEnvironmentDetection();
        };
    </script>
</body>
</html>
