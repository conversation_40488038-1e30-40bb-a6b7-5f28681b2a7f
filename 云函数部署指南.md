# ☁️ uniCloud云函数部署指南

## 🎯 问题解决方案

根据您的错误日志，问题是：**无法连接uniCloud本地调试服务**

这是因为您在APP环境中运行，但uniCloud本地调试服务没有正确启动或配置。

## 🚀 解决步骤

### 方案一：部署云函数到云端（推荐）

#### 1. 在HBuilderX中部署云函数
```bash
1. 右键点击 uniCloud-aliyun/cloudfunctions/voiceRecognition 文件夹
2. 选择 "上传并运行" 或 "上传部署"
3. 等待部署完成
4. 在uniCloud控制台确认函数已部署成功
```

#### 2. 修改项目配置
确保项目已正确关联到uniCloud服务空间：
```bash
1. 右键点击 uniCloud-aliyun 文件夹
2. 选择 "关联云服务空间"
3. 选择或创建一个阿里云服务空间
4. 确认关联成功
```

#### 3. 测试云函数
```javascript
// 在控制台运行以下代码测试
uniCloud.callFunction({
    name: 'voiceRecognition',
    data: {
        test: true
    }
}).then(res => {
    console.log('云函数测试成功:', res);
}).catch(err => {
    console.error('云函数测试失败:', err);
});
```

### 方案二：启用本地调试服务

#### 1. 启动本地调试服务
```bash
1. 在HBuilderX中打开 "运行" 菜单
2. 选择 "运行到内置浏览器" 或 "运行到Chrome"
3. 确保本地调试服务已启动
4. 检查控制台是否有uniCloud连接成功的提示
```

#### 2. 网络配置检查
```bash
1. 确保手机和电脑在同一局域网
2. 检查防火墙设置，允许HBuilderX的nodejs进程
3. 如果切换过网络，重启HBuilderX
```

### 方案三：使用直接API调用（当前已实现）

当云函数不可用时，系统会自动切换到直接API调用模式：

#### 特点：
- ✅ 真实调用硅基流动API
- ✅ 不返回任何模拟数据
- ✅ 自动处理音频文件上传
- ✅ 完整的错误处理机制

#### 实现原理：
```javascript
// 当云函数连接失败时，自动调用
async callSiliconFlowApiDirectly(base64Data, audioFormat, apiKey, apiUrl) {
    // 1. 将base64转换为临时文件
    const tempFilePath = await this.saveBase64ToTempFile(cleanBase64, audioFormat);
    
    // 2. 使用uni.uploadFile上传到硅基流动API
    const uploadResult = await uni.uploadFile({
        url: apiUrl,
        filePath: tempFilePath,
        name: 'file',
        formData: {
            'model': 'FunAudioLLM/SenseVoiceSmall',
            'language': 'zh'
        },
        header: {
            'Authorization': `Bearer ${apiKey}`
        }
    });
    
    // 3. 解析并返回真实的API响应
    return JSON.parse(uploadResult.data);
}
```

## 🔧 验证修复效果

### 1. 测试语音识别功能
```bash
1. 打开语音记账页面
2. 点击"开始录音"
3. 说出测试内容（如："买菜花了30块钱"）
4. 点击"停止录音"
5. 查看识别结果
```

### 2. 检查日志输出
正常情况下应该看到：
```
✅ 启用硅基流动API直接调用模式
✅ 使用uni.uploadFile上传音频文件...
✅ 硅基流动API响应: {...}
✅ API调用成功: {...}
```

### 3. 使用测试工具
打开 `test-real-api.html` 进行API连接测试：
```bash
1. 点击"测试API连接"
2. 点击"测试文件上传"
3. 查看测试结果和日志
```

## 📊 当前状态总结

### ✅ 已修复的问题
1. **TextEncoder兼容性**：修复了uni-app环境中的兼容性问题
2. **真实API调用**：确保直接调用硅基流动API，不返回模拟数据
3. **文件上传处理**：正确处理音频文件的base64转换和上传
4. **错误处理**：提供完整的错误处理和用户提示

### 🎯 推荐使用方案
1. **首选**：部署云函数到云端（性能最佳，功能最完整）
2. **备选**：直接API调用（当云函数不可用时自动启用）

### 📝 注意事项
- 直接API调用在某些网络环境中可能受到CORS限制
- 云函数方案提供更好的安全性和性能
- 建议在生产环境中使用云函数方案

## 🆘 故障排除

### 如果仍然出现问题：

#### 1. 检查API密钥
```javascript
const API_KEY = 'sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl';
// 确认密钥是否有效
```

#### 2. 检查网络连接
```bash
# 测试API端点是否可访问
curl -I https://api.siliconflow.cn/v1/audio/transcriptions
```

#### 3. 查看详细日志
在控制台查看完整的错误信息和调用堆栈。

#### 4. 联系技术支持
如果问题持续存在，请提供：
- 完整的错误日志
- 网络环境描述
- 设备和系统信息
- uniCloud配置截图

## 🎉 预期结果

修复完成后，语音识别功能应该：
1. ✅ 真实调用硅基流动AI语音识别服务
2. ✅ 返回准确的语音识别结果
3. ✅ 智能解析记账信息（金额、类型、日期等）
4. ✅ 提供友好的错误提示和处理

现在您可以正常使用语音记账功能了！🎤💰
