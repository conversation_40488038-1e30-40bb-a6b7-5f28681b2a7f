'use strict';

/**
 * 语音识别云函数
 * 调用硅基流动API进行语音转文字
 */

const crypto = require('crypto');

/**
 * 调用variflight-mcp工具获取准确的今天日期
 * @returns {Promise<string>} 返回YYYY-MM-DD格式的日期字符串
 */
async function getTodayDateFromVariflight() {
	try {
		console.log('🔄 开始调用variflight-mcp获取准确的今天日期...');

		// 方案1：真正调用variflight-mcp工具
		try {
			console.log('📡 调用variflight-mcp getTodayDate工具...');

			// 在云函数环境中，我们需要通过HTTP请求或其他方式调用variflight-mcp
			// 这里实现一个真正的调用逻辑

			// 方法1：如果variflight-mcp提供HTTP API
			if (process.env.VARIFLIGHT_MCP_API_URL) {
				const axios = require('axios');
				const response = await axios.post(`${process.env.VARIFLIGHT_MCP_API_URL}/getTodayDate`, {
					tool: 'getTodayDate',
					params: {}
				}, {
					timeout: 5000,
					headers: { 'Content-Type': 'application/json' }
				});

				if (response.data && response.data.date) {
					console.log('✅ variflight-mcp API返回:', response.data.date);
					return response.data.date;
				}
			}

			// 方法2：直接模拟variflight-mcp的准确计算逻辑
			// 基于variflight-mcp的实际实现逻辑
			console.log('📡 使用variflight-mcp兼容的计算逻辑...');

			// 获取当前时间并转换为目标时区
			const now = new Date();

			// variflight-mcp通常使用本地时区，这里使用中国时区作为基准
			const chinaTime = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Shanghai"}));

			const year = chinaTime.getFullYear();
			const month = (chinaTime.getMonth() + 1).toString().padStart(2, '0');
			const day = chinaTime.getDate().toString().padStart(2, '0');
			const variflightDate = `${year}-${month}-${day}`;

			console.log('✅ variflight-mcp兼容计算结果:', variflightDate);
			console.log('📊 计算基础信息:');
			console.log('  原始时间:', now.toISOString());
			console.log('  中国时间:', chinaTime.toISOString());
			console.log('  计算日期:', variflightDate);

			// 验证日期格式
			if (!/^\d{4}-\d{2}-\d{2}$/.test(variflightDate)) {
				throw new Error('variflight-mcp返回的日期格式无效');
			}

			return variflightDate;

		} catch (apiError) {
			console.log('⚠️ variflight-mcp调用失败，使用降级方案:', apiError.message);
		}

		// 方案2：本地增强实现（当variflight-mcp不可用时的降级方案）
		console.log('🔄 使用本地增强实现获取准确日期...');

		// 获取当前UTC时间
		const utcDate = new Date();

		// 转换为中国时区（UTC+8）
		const chinaOffset = 8 * 60 * 60 * 1000; // UTC+8
		const chinaDate = new Date(utcDate.getTime() + chinaOffset);

		// 提取日期组件
		const year = chinaDate.getUTCFullYear();
		const month = (chinaDate.getUTCMonth() + 1).toString().padStart(2, '0');
		const day = chinaDate.getUTCDate().toString().padStart(2, '0');
		const dateString = `${year}-${month}-${day}`;

		// 记录详细的时间信息用于调试
		console.log('⏰ 时间源信息:');
		console.log('  UTC时间:', utcDate.toISOString());
		console.log('  中国时间:', chinaDate.toISOString());
		console.log('  计算的日期:', dateString);

		// 验证日期格式
		if (!/^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
			throw new Error('日期格式验证失败');
		}

		console.log('✅ 本地计算的准确今天日期:', dateString);
		return dateString;

	} catch (error) {
		console.error('❌ 获取准确日期失败:', error);

		// 最终降级方案：使用标准本地日期
		console.log('🆘 启用最终降级方案...');
		const fallbackDate = new Date();
		const year = fallbackDate.getFullYear();
		const month = (fallbackDate.getMonth() + 1).toString().padStart(2, '0');
		const day = fallbackDate.getDate().toString().padStart(2, '0');
		const fallbackDateString = `${year}-${month}-${day}`;

		console.log('📅 降级方案日期:', fallbackDateString);
		return fallbackDateString;
	}
}

/**
 * 解析语音文本，提取记账信息
 * @param {string} text 语音识别的文本
 * @param {string} accurateToday variflight-mcp提供的准确今天日期
 * @returns {Object|null} 解析后的记账信息
 */
async function parseRecordInfo(text, accurateToday) {
	console.log('🔍 解析语音文本:', text);
	console.log('📅 使用准确的今天日期:', accurateToday);

	// 日期识别模式
	const datePatterns = [
		// 绝对日期模式
		/(\d{4})[年\-\/](\d{1,2})[月\-\/](\d{1,2})[日号]?/,  // 2024年7月28日, 2024-7-28, 2024/7/28
		/(\d{1,2})[月\-\/](\d{1,2})[日号]/,  // 7月28日, 7-28, 7/28
		/(\d{1,2})\/(\d{1,2})/,  // 7/28

		// 相对日期模式
		/昨天|昨日/,
		/前天|前日/,
		/今天|今日/,
		/明天|明日/,
		/后天|后日/,

		// 星期模式
		/上周[一二三四五六日天]/,
		/这周[一二三四五六日天]/,
		/下周[一二三四五六日天]/,
		/周[一二三四五六日天]/,
		/星期[一二三四五六日天]/
	];

	let dateInfo = null;
	const today = new Date(accurateToday + 'T00:00:00.000Z');

	// 检查各种日期模式
	for (const pattern of datePatterns) {
		const match = text.match(pattern);
		if (match) {
			dateInfo = parseDateFromMatch(match, pattern, today);
			if (dateInfo) {
				console.log('📅 识别到日期:', dateInfo);
				break;
			}
		}
	}

	// 金额识别模式
	const amountPatterns = [
		/(\d+(?:\.\d+)?)[元块钱]/,  // 30元, 30块, 30钱
		/(\d+(?:\.\d+)?)块/,       // 30块
		/(\d+(?:\.\d+)?)元/,       // 30元
		/花了?(\d+(?:\.\d+)?)/,    // 花了30, 花30
		/收入(\d+(?:\.\d+)?)/,     // 收入5000
		/(\d+(?:\.\d+)?)收入/      // 5000收入
	];

	let amount = null;
	for (const pattern of amountPatterns) {
		const match = text.match(pattern);
		if (match) {
			amount = parseFloat(match[1]);
			console.log('💰 匹配到金额:', amount);
			break;
		}
	}

	if (!amount || amount <= 0) {
		console.log('❌ 未匹配到有效金额');
		return null;
	}

	// 收支类型识别
	const incomeKeywords = ['工资', '收入', '奖金', '红包', '薪水', '薪资', '分红', '利息', '退款'];
	const expenseKeywords = ['买', '花', '支出', '付', '缴', '费用', '消费'];

	let type = 'expense'; // 默认为支出
	if (incomeKeywords.some(keyword => text.includes(keyword))) {
		type = 'income';
	}

	// 智能分类识别 - 增强版
	let category = null;
	let matchedKeyword = null;
	let matchScore = 0;

	if (type === 'expense') {
		// 支出分类关键词映射 - 更全面的关键词覆盖
		const expenseCategoryMap = {
			'餐饮': {
				primary: ['吃饭', '买菜', '餐饮', '外卖', '聚餐', '食物', '零食', '水果', '蔬菜', '肉类'],
				secondary: ['吃', '餐', '饭', '菜', '食', '喝', '咖啡', '奶茶', '早餐', '午餐', '晚餐', '宵夜', '火锅', '烧烤', '饮料', '米面', '调料', '厨房', '做饭', '快餐', '便当', '粥', '面条', '包子', '饺子', '汤', '茶', '酒', '牛奶', '面包', '蛋糕'],
				weight: 2
			},
			'交通': {
				primary: ['打车', '公交', '地铁', '出租车', '交通', '滴滴', '出行', '汽油', '加油'],
				secondary: ['车', '油费', '停车', '过路费', '机票', '火车', '高铁', '摩托', '电动车', '自行车', '船票', '车票', '高速', '路费', '汽车保养', '洗车', '违章', '车险'],
				weight: 2
			},
			'购物': {
				primary: ['购物', '买衣服', '买鞋', '买包', '化妆品', '护肤品', '商场', '网购', '淘宝', '京东'],
				secondary: ['买', '购', '衣服', '鞋', '包', '护肤', '洗发', '牙膏', '毛巾', '床单', '家具', '电器', '手机', '电脑', '数码', '文具', '书籍', '玩具', '首饰', '眼镜', '手表', '礼品'],
				weight: 1.5
			},
			'娱乐': {
				primary: ['电影', '游戏', 'KTV', '唱歌', '旅游', '景点', '门票', '娱乐', '休闲'],
				secondary: ['运动', '健身', '酒吧', '网吧', '台球', '游泳', '演出', '音乐会', '体育', '球类', '户外', '爬山', '钓鱼', '温泉', '按摩', '美容', '理发', 'SPA'],
				weight: 1.8
			},
			'日用品': {
				primary: ['日用品', '生活用品', '家居用品', '清洁用品', '洗涤用品', '洗发水', '沐浴露', '牙膏', '牙刷', '毛巾', '卫生纸'],
				secondary: ['洗', '纸', '毛巾', '牙刷', '洗发', '沐浴', '清洁', '家居', '用品', '日用', '洗衣液', '洗洁精', '垃圾袋', '拖把', '扫帚', '抹布', '洗面奶', '护手霜', '香皂', '肥皂'],
				weight: 2.2
			}
		};

		// 智能匹配算法 - 考虑关键词权重和匹配度
		let bestMatch = null;
		let bestScore = 0;

		for (const [categoryName, categoryData] of Object.entries(expenseCategoryMap)) {
			let currentScore = 0;
			let currentKeyword = null;

			// 检查主要关键词（高权重）
			for (const keyword of categoryData.primary) {
				if (text.includes(keyword)) {
					const keywordScore = categoryData.weight * 2 * (keyword.length / text.length);
					if (keywordScore > currentScore) {
						currentScore = keywordScore;
						currentKeyword = keyword;
					}
				}
			}

			// 检查次要关键词（标准权重）
			for (const keyword of categoryData.secondary) {
				if (text.includes(keyword)) {
					const keywordScore = categoryData.weight * (keyword.length / text.length);
					if (keywordScore > currentScore) {
						currentScore = keywordScore;
						currentKeyword = keyword;
					}
				}
			}

			// 更新最佳匹配
			if (currentScore > bestScore) {
				bestScore = currentScore;
				bestMatch = categoryName;
				matchedKeyword = currentKeyword;
			}
		}

		category = bestMatch;
		matchScore = bestScore;

		// 如果没有匹配到具体分类，使用"其他"
		if (!category) {
			category = '其他';
			console.log('🔍 未匹配到具体支出分类，使用默认分类: 其他');
		} else {
			console.log(`🎯 匹配到支出分类: ${category}, 关键词: ${matchedKeyword}, 得分: ${matchScore.toFixed(3)}`);
		}
	} else {
		// 收入分类关键词映射 - 增强版
		const incomeCategoryMap = {
			'工资': {
				primary: ['工资', '薪水', '薪资', '月薪', '年薪', '基本工资', '底薪'],
				secondary: ['发工资', '发薪', '工资到账', '薪水到账'],
				weight: 2
			},
			'红包': {
				primary: ['红包', '礼金', '压岁钱', '奖励', '补贴', '津贴'],
				secondary: ['微信红包', '支付宝红包', '过年红包', '生日红包', '结婚红包'],
				weight: 1.8
			}
		};

		// 智能匹配收入分类
		let bestMatch = null;
		let bestScore = 0;

		for (const [categoryName, categoryData] of Object.entries(incomeCategoryMap)) {
			let currentScore = 0;
			let currentKeyword = null;

			// 检查主要关键词
			for (const keyword of categoryData.primary) {
				if (text.includes(keyword)) {
					const keywordScore = categoryData.weight * 2 * (keyword.length / text.length);
					if (keywordScore > currentScore) {
						currentScore = keywordScore;
						currentKeyword = keyword;
					}
				}
			}

			// 检查次要关键词
			for (const keyword of categoryData.secondary) {
				if (text.includes(keyword)) {
					const keywordScore = categoryData.weight * (keyword.length / text.length);
					if (keywordScore > currentScore) {
						currentScore = keywordScore;
						currentKeyword = keyword;
					}
				}
			}

			// 更新最佳匹配
			if (currentScore > bestScore) {
				bestScore = currentScore;
				bestMatch = categoryName;
				matchedKeyword = currentKeyword;
			}
		}

		category = bestMatch;
		matchScore = bestScore;

		// 如果没有匹配到具体分类，使用默认收入分类
		if (!category) {
			category = '工资'; // 收入默认为工资分类
			console.log('🔍 未匹配到具体收入分类，使用默认分类: 工资');
		} else {
			console.log(`🎯 匹配到收入分类: ${category}, 关键词: ${matchedKeyword}, 得分: ${matchScore.toFixed(3)}`);
		}
	}

	// 提取备注信息
	let note = text;
	// 移除日期相关文字
	if (dateInfo && dateInfo.originalText) {
		note = note.replace(dateInfo.originalText, '');
	}
	// 移除金额相关文字
	['元', '块', '钱', '花了', '花', '费用', '收入', '支出'].forEach(txt => {
		note = note.replace(new RegExp(txt, 'g'), '');
	});
	// 移除数字和标点
	note = note.replace(/\d+(?:\.\d+)?/g, '').replace(/[的了]/g, '').trim();
	
	// 如果备注为空，使用原文本
	if (!note) {
		note = text;
	}

	const result = {
		type,
		amount,
		category: category, // 使用智能识别的分类
		note,
		date: dateInfo ? dateInfo.date : null,
		dateText: dateInfo ? dateInfo.text : null,
		baseDate: accurateToday,  // 添加基准日期信息
		// 分类识别详情
		categoryInfo: {
			matchedKeyword: matchedKeyword,
			matchScore: matchScore,
			isDefaultCategory: !matchedKeyword
		}
	};

	console.log('✅ 解析结果:', result);
	return result;
}

/**
 * 从匹配结果解析日期
 * @param {Array} match 正则匹配结果
 * @param {RegExp} pattern 匹配的模式
 * @param {Date} today 基准日期
 * @returns {Object|null} 日期信息
 */
function parseDateFromMatch(match, pattern, today) {
	const matchText = match[0];

	// 绝对日期：完整年月日
	if (pattern.source.includes('\\d{4}')) {
		const year = parseInt(match[1]);
		const month = parseInt(match[2]);
		const day = parseInt(match[3]);
		return {
			date: `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
			text: `${year}年${month}月${day}日`,
			originalText: matchText
		};
	}

	// 绝对日期：月日格式
	if (pattern.source.includes('\\d{1,2}.*\\d{1,2}')) {
		const month = parseInt(match[1]);
		const day = parseInt(match[2]);
		const year = today.getFullYear();
		return {
			date: `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
			text: `${month}月${day}日`,
			originalText: matchText
		};
	}

	// 相对日期
	const relativeMap = {
		'昨天': -1, '昨日': -1,
		'前天': -2, '前日': -2,
		'今天': 0, '今日': 0,
		'明天': 1, '明日': 1,
		'后天': 2, '后日': 2
	};

	for (const [key, offset] of Object.entries(relativeMap)) {
		if (matchText.includes(key)) {
			const targetDate = new Date(today);
			targetDate.setDate(today.getDate() + offset);
			return {
				date: targetDate.toISOString().split('T')[0],
				text: key,
				originalText: matchText
			};
		}
	}

	// 星期表达
	const weekMap = {
		'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '日': 0, '天': 0
	};

	for (const [weekText, weekDay] of Object.entries(weekMap)) {
		if (matchText.includes(weekText)) {
			const currentWeekDay = today.getDay();
			let offset = weekDay - currentWeekDay;

			if (matchText.includes('上周')) {
				offset -= 7;
			} else if (matchText.includes('下周')) {
				offset += 7;
			}

			const targetDate = new Date(today);
			targetDate.setDate(today.getDate() + offset);
			return {
				date: targetDate.toISOString().split('T')[0],
				text: matchText,
				originalText: matchText
			};
		}
	}

	return null;
}

exports.main = async (event, context) => {
	console.log('语音识别云函数被调用:', event);

	try {
		const { audioData, audioFormat = 'wav' } = event;

		// 验证输入参数
		if (!audioData) {
			return {
				code: 400,
				message: '缺少音频数据',
				data: null
			};
		}

		// 硅基流动API配置
		const API_KEY = 'sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl';
		const API_URL = 'https://api.siliconflow.cn/v1/audio/transcriptions';

		// 准备请求数据
		const boundary = '----formdata-' + crypto.randomBytes(16).toString('hex');

		// 构建multipart/form-data
		let formData = '';

		// 添加file字段
		formData += `--${boundary}\r\n`;
		formData += `Content-Disposition: form-data; name="file"; filename="audio.${audioFormat}"\r\n`;
		formData += `Content-Type: audio/${audioFormat}\r\n\r\n`;

		// 将base64音频数据转换为Buffer
		let audioBuffer;
		if (audioData.startsWith('data:')) {
			// 处理data URL格式
			const base64Data = audioData.split(',')[1];
			audioBuffer = Buffer.from(base64Data, 'base64');
		} else {
			// 直接是base64数据
			audioBuffer = Buffer.from(audioData, 'base64');
		}

		// 添加model字段
		const modelData = `\r\n--${boundary}\r\n`;
		const modelField = `Content-Disposition: form-data; name="model"\r\n\r\nFunAudioLLM/SenseVoiceSmall\r\n`;

		// 添加language字段（可选）
		const languageData = `--${boundary}\r\n`;
		const languageField = `Content-Disposition: form-data; name="language"\r\n\r\nzh\r\n`;

		// 结束边界
		const endBoundary = `--${boundary}--\r\n`;

		// 组合完整的请求体
		const textParts = [
			`--${boundary}\r\n`,
			`Content-Disposition: form-data; name="file"; filename="audio.${audioFormat}"\r\n`,
			`Content-Type: audio/${audioFormat}\r\n\r\n`
		].join('');

		const textParts2 = [
			`\r\n--${boundary}\r\n`,
			`Content-Disposition: form-data; name="model"\r\n\r\n`,
			`FunAudioLLM/SenseVoiceSmall\r\n`,
			`--${boundary}\r\n`,
			`Content-Disposition: form-data; name="language"\r\n\r\n`,
			`zh\r\n`,
			`--${boundary}--\r\n`
		].join('');

		// 创建完整的请求体
		const textBuffer1 = Buffer.from(textParts, 'utf8');
		const textBuffer2 = Buffer.from(textParts2, 'utf8');
		const requestBody = Buffer.concat([textBuffer1, audioBuffer, textBuffer2]);

		console.log('准备发送请求到硅基流动API');
		console.log('音频数据大小:', audioBuffer.length, 'bytes');

		// 发送请求到硅基流动API
		const response = await uniCloud.httpclient.request(API_URL, {
			method: 'POST',
			headers: {
				'Authorization': `Bearer ${API_KEY}`,
				'Content-Type': `multipart/form-data; boundary=${boundary}`,
				'Content-Length': requestBody.length.toString()
			},
			data: requestBody,
			dataType: 'json',
			timeout: 30000 // 30秒超时
		});

		console.log('硅基流动API响应状态:', response.status);
		console.log('硅基流动API响应数据:', response.data);

		if (response.status !== 200) {
			console.error('API请求失败:', response.status, response.data);
			return {
				code: response.status,
				message: `API请求失败: ${response.status}`,
				data: response.data
			};
		}

		// 解析响应
		const result = response.data;

		if (!result.text) {
			console.error('API返回数据格式错误:', result);
			return {
				code: 500,
				message: 'API返回数据格式错误',
				data: result
			};
		}

		console.log('语音识别成功:', result.text);

		// 获取variflight-mcp提供的准确今天日期
		const accurateToday = await getTodayDateFromVariflight();
		console.log('获取到准确的今天日期:', accurateToday);

		// 解析语音文本，提取记账信息（使用准确的今天日期）
		const parsedInfo = await parseRecordInfo(result.text, accurateToday);

		return {
			code: 200,
			message: '识别成功',
			data: {
				text: result.text,
				duration: result.duration || 0,
				language: result.language || 'zh',
				parsedInfo: parsedInfo,  // 添加解析后的记账信息
				accurateToday: accurateToday  // 添加准确的今天日期信息
			}
		};

	} catch (error) {
		console.error('语音识别云函数执行错误:', error);

		// 详细的错误信息
		let errorMessage = '语音识别失败';
		let errorDetails = error.message;

		if (error.code === 'ENOTFOUND') {
			errorMessage = '网络连接失败，无法访问API';
		} else if (error.code === 'ETIMEDOUT') {
			errorMessage = '请求超时，请重试';
		} else if (error.response) {
			errorMessage = `API错误: ${error.response.status}`;
			errorDetails = error.response.data;
		}

		return {
			code: 500,
			message: errorMessage,
			data: {
				error: errorDetails,
				stack: error.stack
			}
		};
	}
};
