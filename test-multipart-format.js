/**
 * 测试multipart/form-data格式是否符合硅基流动API要求
 */

const https = require('https');
const crypto = require('crypto');
const fs = require('fs');

// 硅基流动API配置
const API_KEY = 'sk-gjijzuaoykqwxfpjriihbiiciuhaoemagjtwlceyxgcrwiyl';
const API_URL = 'https://api.siliconflow.cn/v1/audio/transcriptions';

/**
 * 创建一个真实的音频文件用于测试
 */
function createRealAudioBuffer() {
    // 创建一个更真实的WAV文件，包含一些音频数据
    const sampleRate = 16000;
    const duration = 1; // 1秒
    const numSamples = sampleRate * duration;
    
    // WAV文件头（44字节）
    const wavHeader = Buffer.alloc(44);
    const dataSize = numSamples * 2; // 16位音频，每样本2字节
    const fileSize = 36 + dataSize;
    
    // RIFF头
    wavHeader.write('RIFF', 0);
    wavHeader.writeUInt32LE(fileSize, 4);
    wavHeader.write('WAVE', 8);
    
    // fmt块
    wavHeader.write('fmt ', 12);
    wavHeader.writeUInt32LE(16, 16); // fmt块大小
    wavHeader.writeUInt16LE(1, 20);  // PCM格式
    wavHeader.writeUInt16LE(1, 22);  // 单声道
    wavHeader.writeUInt32LE(sampleRate, 24); // 采样率
    wavHeader.writeUInt32LE(sampleRate * 2, 28); // 字节率
    wavHeader.writeUInt16LE(2, 32);  // 块对齐
    wavHeader.writeUInt16LE(16, 34); // 位深度
    
    // data块
    wavHeader.write('data', 36);
    wavHeader.writeUInt32LE(dataSize, 40);
    
    // 生成音频数据（简单的正弦波）
    const audioData = Buffer.alloc(dataSize);
    for (let i = 0; i < numSamples; i++) {
        const sample = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 16000; // 440Hz正弦波
        audioData.writeInt16LE(Math.round(sample), i * 2);
    }
    
    return Buffer.concat([wavHeader, audioData]);
}

/**
 * 测试云函数中使用的multipart格式
 */
async function testCloudFunctionFormat() {
    console.log('🧪 测试云函数multipart格式...');
    
    try {
        // 创建测试音频数据
        const audioBuffer = createRealAudioBuffer();
        const audioFormat = 'wav';
        
        console.log('📦 音频数据大小:', audioBuffer.length, 'bytes');
        
        // 使用与云函数相同的格式构建请求
        const boundary = '----formdata-' + crypto.randomBytes(16).toString('hex');
        
        // 组合完整的请求体（与云函数逻辑一致）
        const textParts = [
            `--${boundary}\r\n`,
            `Content-Disposition: form-data; name="file"; filename="audio.${audioFormat}"\r\n`,
            `Content-Type: audio/${audioFormat}\r\n\r\n`
        ].join('');

        const textParts2 = [
            `\r\n--${boundary}\r\n`,
            `Content-Disposition: form-data; name="model"\r\n\r\n`,
            `FunAudioLLM/SenseVoiceSmall\r\n`,
            `--${boundary}\r\n`,
            `Content-Disposition: form-data; name="language"\r\n\r\n`,
            `zh\r\n`,
            `--${boundary}--\r\n`
        ].join('');

        // 创建完整的请求体
        const textBuffer1 = Buffer.from(textParts, 'utf8');
        const textBuffer2 = Buffer.from(textParts2, 'utf8');
        const requestBody = Buffer.concat([textBuffer1, audioBuffer, textBuffer2]);
        
        console.log('📋 请求体总大小:', requestBody.length, 'bytes');
        console.log('🏷️ Boundary:', boundary);
        
        // 发送请求
        const response = await makeHttpRequest(API_URL, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': `multipart/form-data; boundary=${boundary}`,
                'Content-Length': requestBody.length.toString()
            }
        }, requestBody);
        
        console.log('✅ API响应状态:', response.statusCode);
        console.log('📝 响应数据:', JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 200) {
            console.log('🎉 multipart格式测试成功！');
            console.log('🎵 识别结果:', response.data.text);
            return true;
        } else {
            console.log('❌ multipart格式可能有问题');
            return false;
        }
        
    } catch (error) {
        console.error('❌ multipart格式测试失败:', error.message);
        return false;
    }
}

/**
 * 测试简化的multipart格式
 */
async function testSimplifiedFormat() {
    console.log('🧪 测试简化multipart格式...');
    
    try {
        // 创建测试音频数据
        const audioBuffer = createRealAudioBuffer();
        
        // 使用更简单的格式
        const boundary = '----WebKitFormBoundary' + crypto.randomBytes(16).toString('hex');
        
        let formData = '';
        
        // 添加file字段
        formData += `--${boundary}\r\n`;
        formData += `Content-Disposition: form-data; name="file"; filename="test.wav"\r\n`;
        formData += `Content-Type: audio/wav\r\n\r\n`;
        
        // 音频数据将在这里插入
        const filePart = Buffer.from(formData, 'utf8');
        
        // 添加model字段
        let modelPart = `\r\n--${boundary}\r\n`;
        modelPart += `Content-Disposition: form-data; name="model"\r\n\r\n`;
        modelPart += `FunAudioLLM/SenseVoiceSmall`;
        
        // 结束
        let endPart = `\r\n--${boundary}--\r\n`;
        
        const modelBuffer = Buffer.from(modelPart, 'utf8');
        const endBuffer = Buffer.from(endPart, 'utf8');
        
        const requestBody = Buffer.concat([filePart, audioBuffer, modelBuffer, endBuffer]);
        
        console.log('📋 简化请求体大小:', requestBody.length, 'bytes');
        
        // 发送请求
        const response = await makeHttpRequest(API_URL, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${API_KEY}`,
                'Content-Type': `multipart/form-data; boundary=${boundary}`,
                'Content-Length': requestBody.length.toString()
            }
        }, requestBody);
        
        console.log('✅ 简化格式响应状态:', response.statusCode);
        console.log('📝 响应数据:', JSON.stringify(response.data, null, 2));
        
        if (response.statusCode === 200) {
            console.log('🎉 简化multipart格式测试成功！');
            console.log('🎵 识别结果:', response.data.text);
            return true;
        } else {
            console.log('❌ 简化格式也有问题');
            return false;
        }
        
    } catch (error) {
        console.error('❌ 简化格式测试失败:', error.message);
        return false;
    }
}

/**
 * 发送HTTP请求
 */
function makeHttpRequest(url, options, data) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || 443,
            path: urlObj.pathname,
            method: options.method,
            headers: options.headers,
            timeout: 30000
        };
        
        const req = https.request(requestOptions, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                try {
                    const parsedData = JSON.parse(responseData);
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: parsedData
                    });
                } catch (e) {
                    resolve({
                        statusCode: res.statusCode,
                        headers: res.headers,
                        data: responseData
                    });
                }
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (data) {
            req.write(data);
        }
        
        req.end();
    });
}

// 主测试函数
async function main() {
    console.log('🚀 开始multipart格式诊断测试\n');
    
    // 测试云函数格式
    const cloudFunctionOk = await testCloudFunctionFormat();
    console.log('');
    
    // 如果云函数格式失败，测试简化格式
    if (!cloudFunctionOk) {
        const simplifiedOk = await testSimplifiedFormat();
        console.log('');
        
        console.log('📊 测试结果:');
        console.log('- 云函数格式:', cloudFunctionOk ? '✅ 成功' : '❌ 失败');
        console.log('- 简化格式:', simplifiedOk ? '✅ 成功' : '❌ 失败');
    } else {
        console.log('📊 测试结果:');
        console.log('- 云函数格式: ✅ 成功');
    }
}

// 运行测试
if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    testCloudFunctionFormat,
    testSimplifiedFormat
};
