"use strict";
const db = uniCloud.database();

/**
 * 确保数据库中存在所有必要的分类
 * 这个云函数用于在现有系统中添加缺失的分类，特别是"其他"分类
 */
exports.main = async () => {
  try {
    const collection = db.collection("categories");

    // 需要确保存在的分类列表
    const requiredCategories = [
      {
        name: "其他",
        type: "expense",
        group_id: "daily",
        icon: "ellipsis-h",
        order: 6,
        description: "无法归类到其他具体分类的支出"
      },
      {
        name: "其他收入",
        type: "income",
        group_id: "daily",
        icon: "coins",
        order: 3,
        description: "无法归类到其他具体分类的收入"
      }
    ];

    let addedCount = 0;
    let existingCount = 0;

    for (const category of requiredCategories) {
      // 检查分类是否已存在
      const existing = await collection.where({
        name: category.name,
        type: category.type,
        group_id: category.group_id
      }).get();

      if (existing.data.length === 0) {
        // 分类不存在，添加它
        await collection.add({
          ...category,
          created_at: new Date(),
          updated_at: new Date(),
          is_deleted: false
        });
        addedCount++;
        console.log(`✅ 添加分类: ${category.name} (${category.type})`);
      } else {
        existingCount++;
        console.log(`ℹ️ 分类已存在: ${category.name} (${category.type})`);
      }
    }

    return {
      code: 0,
      message: `分类检查完成: 新增${addedCount}个，已存在${existingCount}个`,
      data: {
        added: addedCount,
        existing: existingCount,
        total: requiredCategories.length
      }
    };

  } catch (error) {
    console.error('❌ 分类更新失败:', error);
    return {
      code: -1,
      message: "更新失败: " + error.message,
      error: error.stack
    };
  }
};
