<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>云函数连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .button {
            background: #007AFF;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: background 0.3s;
        }
        .button:hover {
            background: #0056CC;
        }
        .button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: #f0f0f0;
            border-left: 4px solid #007AFF;
        }
        .success {
            background: #e8f5e8;
            border-left-color: #28a745;
        }
        .error {
            background: #ffe6e6;
            border-left-color: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .info-box {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>☁️ uniCloud云函数连接测试</h1>
        
        <div class="info-box">
            <h3>📋 测试说明</h3>
            <p>此工具用于测试uniCloud云函数的连接状态和语音识别功能。</p>
            <p><strong>注意：</strong>此页面需要在支持uniCloud的环境中运行（如HBuilderX内置浏览器）。</p>
        </div>
        
        <div>
            <button id="testBtn" class="button">测试云函数连接</button>
            <button id="testVoiceBtn" class="button">测试语音识别云函数</button>
            <button id="clearBtn" class="button">清除日志</button>
        </div>
        
        <div id="status" class="status">准备测试</div>
        
        <h3>📋 测试日志</h3>
        <div id="log" class="log">点击测试按钮开始检查云函数连接状态...</div>
        
        <div class="info-box">
            <h3>🔧 故障排除</h3>
            <ul>
                <li><strong>云函数不存在</strong>：请确保已在uniCloud控制台创建并部署voiceRecognition云函数</li>
                <li><strong>网络连接失败</strong>：检查网络连接和uniCloud服务状态</li>
                <li><strong>权限问题</strong>：确认项目已正确关联uniCloud服务空间</li>
                <li><strong>依赖缺失</strong>：检查云函数的package.json和node_modules</li>
            </ul>
        </div>
    </div>

    <script>
        class CloudFunctionTester {
            constructor() {
                this.testBtn = document.getElementById('testBtn');
                this.testVoiceBtn = document.getElementById('testVoiceBtn');
                this.clearBtn = document.getElementById('clearBtn');
                this.statusDiv = document.getElementById('status');
                this.logDiv = document.getElementById('log');
                
                this.bindEvents();
                this.checkEnvironment();
            }
            
            bindEvents() {
                this.testBtn.addEventListener('click', () => this.testCloudFunction());
                this.testVoiceBtn.addEventListener('click', () => this.testVoiceRecognition());
                this.clearBtn.addEventListener('click', () => this.clearLog());
            }
            
            checkEnvironment() {
                if (typeof uniCloud === 'undefined') {
                    this.log('❌ 错误：uniCloud环境不可用');
                    this.log('请在支持uniCloud的环境中运行此测试（如HBuilderX内置浏览器）');
                    this.updateStatus('环境检查失败', 'error');
                    this.testBtn.disabled = true;
                    this.testVoiceBtn.disabled = true;
                } else {
                    this.log('✅ uniCloud环境检查通过');
                    this.updateStatus('环境检查通过', 'success');
                }
            }
            
            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                this.logDiv.textContent += `[${timestamp}] ${message}\n`;
                this.logDiv.scrollTop = this.logDiv.scrollHeight;
                console.log(message);
            }
            
            clearLog() {
                this.logDiv.textContent = '';
            }
            
            updateStatus(message, type = 'info') {
                this.statusDiv.textContent = message;
                this.statusDiv.className = `status ${type}`;
            }
            
            async testCloudFunction() {
                this.log('🔄 开始测试云函数基本连接...');
                this.updateStatus('正在测试云函数连接...', 'info');
                
                try {
                    // 测试一个简单的云函数调用
                    const result = await uniCloud.callFunction({
                        name: 'voiceRecognition',
                        data: {
                            test: true
                        }
                    });
                    
                    this.log('✅ 云函数连接成功');
                    this.log(`响应数据: ${JSON.stringify(result, null, 2)}`);
                    this.updateStatus('云函数连接正常', 'success');
                    
                } catch (error) {
                    this.log('❌ 云函数连接失败');
                    this.log(`错误信息: ${error.message}`);
                    this.log(`错误详情: ${JSON.stringify(error, null, 2)}`);
                    this.updateStatus('云函数连接失败', 'error');
                    
                    // 提供故障排除建议
                    this.provideTroubleshootingAdvice(error);
                }
            }
            
            async testVoiceRecognition() {
                this.log('🎤 开始测试语音识别云函数...');
                this.updateStatus('正在测试语音识别功能...', 'info');
                
                try {
                    // 创建一个测试用的base64音频数据（简单的WAV文件头）
                    const testAudioBase64 = this.createTestAudioBase64();
                    
                    this.log('📦 准备测试音频数据...');
                    this.log(`测试数据长度: ${testAudioBase64.length} 字符`);
                    
                    const result = await uniCloud.callFunction({
                        name: 'voiceRecognition',
                        data: {
                            audioData: testAudioBase64,
                            audioFormat: 'wav'
                        }
                    });
                    
                    this.log('✅ 语音识别云函数调用成功');
                    this.log(`响应数据: ${JSON.stringify(result, null, 2)}`);
                    
                    if (result.result && result.result.code === 200) {
                        this.log('🎉 语音识别功能正常工作');
                        this.updateStatus('语音识别功能正常', 'success');
                    } else {
                        this.log('⚠️ 语音识别返回异常结果');
                        this.updateStatus('语音识别功能异常', 'error');
                    }
                    
                } catch (error) {
                    this.log('❌ 语音识别云函数测试失败');
                    this.log(`错误信息: ${error.message}`);
                    this.log(`错误详情: ${JSON.stringify(error, null, 2)}`);
                    this.updateStatus('语音识别测试失败', 'error');
                    
                    this.provideTroubleshootingAdvice(error);
                }
            }
            
            createTestAudioBase64() {
                // 创建一个简单的WAV文件头的base64编码
                // 这只是用于测试云函数连接，不是真实的音频数据
                const wavHeader = new ArrayBuffer(44);
                const view = new DataView(wavHeader);
                
                // WAV文件头
                const writeString = (offset, string) => {
                    for (let i = 0; i < string.length; i++) {
                        view.setUint8(offset + i, string.charCodeAt(i));
                    }
                };
                
                writeString(0, 'RIFF');
                view.setUint32(4, 36, true);
                writeString(8, 'WAVE');
                writeString(12, 'fmt ');
                view.setUint32(16, 16, true);
                view.setUint16(20, 1, true);
                view.setUint16(22, 1, true);
                view.setUint32(24, 16000, true);
                view.setUint32(28, 32000, true);
                view.setUint16(32, 2, true);
                view.setUint16(34, 16, true);
                writeString(36, 'data');
                view.setUint32(40, 0, true);
                
                // 转换为base64
                const bytes = new Uint8Array(wavHeader);
                let binary = '';
                for (let i = 0; i < bytes.byteLength; i++) {
                    binary += String.fromCharCode(bytes[i]);
                }
                return btoa(binary);
            }
            
            provideTroubleshootingAdvice(error) {
                this.log('\n🔧 故障排除建议:');
                
                if (error.message.includes('云函数不存在')) {
                    this.log('- 请确保voiceRecognition云函数已正确创建和部署');
                    this.log('- 检查云函数名称是否正确');
                } else if (error.message.includes('网络')) {
                    this.log('- 检查网络连接状态');
                    this.log('- 确认uniCloud服务是否正常');
                } else if (error.message.includes('权限')) {
                    this.log('- 检查项目是否已关联到正确的uniCloud服务空间');
                    this.log('- 确认云函数的访问权限配置');
                } else {
                    this.log('- 查看uniCloud控制台的云函数日志');
                    this.log('- 检查云函数代码是否有语法错误');
                    this.log('- 确认云函数的依赖包是否正确安装');
                }
                
                this.log('\n📞 如需进一步帮助，请提供完整的错误日志信息。');
            }
        }
        
        // 初始化测试器
        document.addEventListener('DOMContentLoaded', () => {
            new CloudFunctionTester();
        });
    </script>
</body>
</html>
