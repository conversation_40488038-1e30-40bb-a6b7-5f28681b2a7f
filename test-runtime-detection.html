<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>运行时检测测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .log {
            background: #f8f8f8;
            border: 1px solid #ddd;
            padding: 15px;
            border-radius: 4px;
            margin-top: 20px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 运行时检测测试</h1>
        <p>测试修复后的语音识别兼容性代码是否能正确检测H5环境并避免调用不支持的API</p>
        
        <button class="test-button" onclick="testRuntimeDetection()">测试运行时检测</button>
        <button class="test-button" onclick="testFileSystemManager()">测试文件系统管理器</button>
        <button class="test-button" onclick="clearLog()">清空日志</button>
        
        <div id="status" class="status info">准备测试</div>
        
        <h3>📋 测试日志</h3>
        <div id="log" class="log">点击测试按钮开始验证运行时检测...</div>
    </div>

    <script>
        // 模拟uni-app环境检测
        class SpeechRecognitionCompat {
            constructor() {
                this.platform = this.detectPlatform();
                this.log(`检测到平台: ${this.platform}`);
            }

            detectPlatform() {
                // 模拟uni-app的平台检测逻辑
                if (typeof window !== 'undefined' && window.location) {
                    return 'H5';
                } else if (typeof wx !== 'undefined' && wx.miniProgram) {
                    return 'MP-WEIXIN';
                } else if (typeof plus !== 'undefined') {
                    return 'APP-PLUS';
                } else {
                    return 'UNKNOWN';
                }
            }

            log(message) {
                const timestamp = new Date().toLocaleTimeString();
                const logElement = document.getElementById('log');
                logElement.textContent += `[${timestamp}] ${message}\n`;
                logElement.scrollTop = logElement.scrollHeight;
                console.log(message);
            }

            updateStatus(message, type) {
                const statusElement = document.getElementById('status');
                statusElement.textContent = message;
                statusElement.className = `status ${type}`;
            }

            // 模拟修复后的fileToBase64方法
            async fileToBase64(filePath) {
                return new Promise((resolve, reject) => {
                    this.log(`尝试读取文件: ${filePath}`);
                    
                    // 运行时检测环境
                    if (this.platform === 'H5' || typeof uni === 'undefined' || !uni.getFileSystemManager) {
                        this.log('✅ H5环境检测成功，跳过uni.getFileSystemManager调用');
                        
                        // H5环境，如果传入的已经是base64数据，直接返回
                        if (filePath.startsWith('data:')) {
                            this.log('✅ 检测到base64数据，直接返回');
                            resolve(filePath);
                            return;
                        }
                        // H5环境不支持文件路径读取，返回错误
                        this.log('⚠️ H5环境不支持文件路径读取');
                        reject(new Error('H5环境不支持文件路径读取'));
                        return;
                    }

                    // 其他环境的处理逻辑
                    this.log('其他环境，尝试使用uni.getFileSystemManager');
                    // 这里在真实环境中会调用uni.getFileSystemManager
                    resolve('模拟的base64数据');
                });
            }

            // 模拟修复后的saveBase64ToTempFile方法
            async saveBase64ToTempFile(base64Data, audioFormat) {
                return new Promise((resolve, reject) => {
                    this.log(`尝试保存临时文件，格式: ${audioFormat}`);
                    
                    // 运行时检测环境
                    if (this.platform === 'H5' || typeof uni === 'undefined' || !uni.getFileSystemManager) {
                        // H5环境不支持文件系统操作，直接返回base64数据
                        this.log('✅ H5环境检测成功，跳过文件系统操作');
                        this.log('✅ 直接返回base64数据');
                        resolve(base64Data);
                        return;
                    }

                    // 其他环境的处理逻辑
                    this.log('其他环境，尝试使用文件系统管理器');
                    resolve('模拟的临时文件路径');
                });
            }

            // 模拟修复后的createTempAudioFile方法
            async createTempAudioFile(base64Data, audioFormat) {
                return new Promise((resolve, reject) => {
                    this.log(`尝试创建临时音频文件，格式: ${audioFormat}`);
                    
                    // 运行时检测环境
                    if (this.platform === 'H5' || typeof uni === 'undefined' || !uni.getFileSystemManager) {
                        // H5环境不支持文件系统操作，直接返回base64数据
                        this.log('✅ H5环境检测成功，跳过文件系统操作');
                        this.log('✅ 直接返回base64数据');
                        resolve(base64Data);
                        return;
                    }

                    // 其他环境的处理逻辑
                    this.log('其他环境，尝试创建临时文件');
                    resolve('模拟的临时文件路径');
                });
            }
        }

        let speechCompat;

        function testRuntimeDetection() {
            const logElement = document.getElementById('log');
            logElement.textContent = '';
            
            try {
                speechCompat = new SpeechRecognitionCompat();
                speechCompat.updateStatus('运行时检测测试通过', 'success');
                
                // 测试各种方法
                testFileOperations();
                
            } catch (error) {
                speechCompat.log(`❌ 运行时检测失败: ${error.message}`);
                speechCompat.updateStatus('运行时检测测试失败', 'error');
            }
        }

        async function testFileOperations() {
            if (!speechCompat) {
                speechCompat = new SpeechRecognitionCompat();
            }

            try {
                // 测试fileToBase64
                speechCompat.log('\n=== 测试fileToBase64 ===');
                try {
                    const result1 = await speechCompat.fileToBase64('data:audio/mp3;base64,test');
                    speechCompat.log(`✅ base64数据处理成功: ${result1.substring(0, 50)}...`);
                } catch (error) {
                    speechCompat.log(`✅ 预期的错误: ${error.message}`);
                }

                try {
                    await speechCompat.fileToBase64('/path/to/file.mp3');
                } catch (error) {
                    speechCompat.log(`✅ 预期的错误: ${error.message}`);
                }

                // 测试saveBase64ToTempFile
                speechCompat.log('\n=== 测试saveBase64ToTempFile ===');
                const result2 = await speechCompat.saveBase64ToTempFile('test-base64-data', 'mp3');
                speechCompat.log(`✅ 保存临时文件成功: ${result2}`);

                // 测试createTempAudioFile
                speechCompat.log('\n=== 测试createTempAudioFile ===');
                const result3 = await speechCompat.createTempAudioFile('data:audio/mp3;base64,test-data', 'mp3');
                speechCompat.log(`✅ 创建临时音频文件成功: ${result3}`);

                speechCompat.log('\n🎉 所有测试通过！H5环境兼容性修复成功！');
                speechCompat.updateStatus('所有测试通过', 'success');

            } catch (error) {
                speechCompat.log(`❌ 文件操作测试失败: ${error.message}`);
                speechCompat.updateStatus('文件操作测试失败', 'error');
            }
        }

        function testFileSystemManager() {
            const logElement = document.getElementById('log');
            logElement.textContent = '';
            
            if (!speechCompat) {
                speechCompat = new SpeechRecognitionCompat();
            }

            speechCompat.log('=== 测试文件系统管理器检测 ===');
            
            // 检测uni对象
            if (typeof uni === 'undefined') {
                speechCompat.log('✅ uni对象未定义（H5环境正常）');
            } else {
                speechCompat.log('⚠️ uni对象已定义');
                
                if (uni.getFileSystemManager) {
                    speechCompat.log('⚠️ uni.getFileSystemManager存在');
                } else {
                    speechCompat.log('✅ uni.getFileSystemManager不存在（H5环境正常）');
                }
            }

            // 检测平台
            speechCompat.log(`当前平台: ${speechCompat.platform}`);
            
            if (speechCompat.platform === 'H5') {
                speechCompat.log('✅ 平台检测正确，H5环境');
                speechCompat.updateStatus('文件系统管理器检测通过', 'success');
            } else {
                speechCompat.log('⚠️ 平台检测异常');
                speechCompat.updateStatus('文件系统管理器检测异常', 'error');
            }
        }

        function clearLog() {
            document.getElementById('log').textContent = '';
            document.getElementById('status').textContent = '日志已清空';
            document.getElementById('status').className = 'status info';
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            testRuntimeDetection();
        };
    </script>
</body>
</html>
